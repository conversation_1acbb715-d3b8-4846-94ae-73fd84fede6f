// Copyright (c) 2024 animsi Technology Co., Ltd. All rights reserved.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

#ifndef MODULE__BLUETOOTH_MANAGER_HPP_
#define MODULE__BLUETOOTH_MANAGER_HPP_

#include "common/base_module.hpp"
#include "utils/config_manager.hpp"
#include <atomic>
#include <memory>
#include <string>
#include <thread>
#include <map>
#include <functional>
#include <mutex>
#include <condition_variable>
#include <dbus/dbus.h>

namespace aby_box {

// GATT服务和特征值UUID定义
constexpr const char* DEVICE_SERVICE_UUID = "12345678-1234-1234-1234-123456789abc";
constexpr const char* WIFI_CHARACTERISTIC_UUID = "12345678-1234-1234-1234-123456789abd";
constexpr const char* DEVICE_REG_CHARACTERISTIC_UUID = "12345678-1234-1234-1234-123456789abe";

// DBus路径定义
constexpr const char* BLUEZ_SERVICE = "org.bluez";
constexpr const char* BLUEZ_ADAPTER_INTERFACE = "org.bluez.Adapter1";
constexpr const char* BLUEZ_GATT_MANAGER_INTERFACE = "org.bluez.GattManager1";
constexpr const char* BLUEZ_ADVERTISING_MANAGER_INTERFACE = "org.bluez.LEAdvertisingManager1";

// 自定义GATT服务路径
constexpr const char* GATT_SERVICE_PATH = "/com/animsi/abybox/service";
constexpr const char* WIFI_CHAR_PATH = "/com/animsi/abybox/service/wifi_char";
constexpr const char* DEVICE_REG_CHAR_PATH = "/com/animsi/abybox/service/device_reg_char";

struct BluetoothConfig {
    std::string device_name = "AbyBox";
    std::string adapter_path = "/org/bluez/hci0";
    bool auto_advertise = true;
    int advertising_timeout = 0; // 0 表示永久广播
};

struct WifiInfo {
    std::string ssid;
    std::string password;
    bool is_valid = false;
};

struct DeviceRegInfo {
    std::string user_id;
    bool is_valid = false;
};

class BluetoothManager : public BaseModule {
public:
    explicit BluetoothManager(const std::string &module_name);
    ~BluetoothManager();

    bool init() override;
    bool start() override;
    bool stop() override;
    void join() override;

    // 配置方法
    void set_bluetooth_config(const BluetoothConfig &config) { config_ = config; }
    
    // 获取当前连接状态
    bool is_connected() const { return is_connected_; }
    
    // 获取WiFi和设备注册信息
    WifiInfo get_wifi_info() const;
    DeviceRegInfo get_device_reg_info() const;

private:
    // 初始化和清理方法
    bool init_bluetooth();
    void cleanup_bluetooth();
    
    // 蓝牙控制方法（嵌入式命令）
    bool disable_classic_bluetooth();
    bool enable_ble_mode();
    bool setup_gatt_server();
    bool start_advertising();
    bool stop_advertising();

    // 广告管理器相关方法
    bool register_advertisement();
    bool unregister_advertisement();
    bool setup_advertisement_object();
    
    // GATT服务器相关方法
    bool register_gatt_application();
    bool register_gatt_service();
    bool register_characteristics();
    
    // DBus消息处理
    static DBusHandlerResult gatt_message_filter(DBusConnection* connection, 
                                                DBusMessage* message, 
                                                void* user_data);
    void handle_gatt_message(DBusMessage* message);
    
    // 特征值数据处理
    bool process_wifi_data(const std::string &data);
    bool process_device_reg_data(const std::string &data);
    bool validate_wifi_info(const WifiInfo &info);
    bool validate_device_reg_info(const DeviceRegInfo &info);
    

    
    // 嵌入式蓝牙命令方法
    bool execute_disable_classic_bt();
    bool execute_enable_ble();
    bool execute_start_gatt_server();
    bool execute_stop_gatt_server();
    
    // 工具方法
    std::string execute_command(const std::string &command);
    bool call_dbus_method(const std::string& service, const std::string& path,
                         const std::string& interface, const std::string& method,
                         DBusMessage** reply = nullptr);
    bool check_bluez_adapter_ready();
    
    // 线程工作方法
    void bluetooth_main_loop();
    void connection_monitor_loop();
    
    // DBus连接管理
    bool connect_to_dbus();
    void disconnect_from_dbus();
    bool setup_dbus_object_paths();
    void unregister_dbus_object_paths();
    bool register_dbus_object_path(const std::string& path);
    
    // DBus接口处理
    void handle_properties_get_all(DBusMessage* message);
    void handle_properties_get(DBusMessage* message);
    void handle_characteristic_read_value(DBusMessage* message);
    void handle_characteristic_write_value(DBusMessage* message);
    void handle_introspect(DBusMessage* message);
    void handle_get_managed_objects(DBusMessage* message);
    void add_service_object_to_managed_objects(DBusMessageIter* objects_array);
    void add_characteristic_to_managed_objects(DBusMessageIter* dict_iter, 
                                              const char* char_path, 
                                              const char* char_uuid, 
                                              const char* service_path);
    std::string get_introspect_xml(const std::string& path);
    
    // DBus对象处理
    static DBusObjectPathVTable dbus_vtable_;
    
    // 成员变量
    BluetoothConfig config_;
    std::unique_ptr<std::thread> main_thread_;
    std::atomic<bool> is_running_{false};
    std::atomic<bool> is_connected_{false};
    std::atomic<bool> is_advertising_{false};
    
    // DBus相关
    DBusConnection* dbus_connection_{nullptr};
    std::mutex dbus_mutex_;
    
    // 数据存储
    mutable std::mutex wifi_info_mutex_;
    mutable std::mutex device_reg_info_mutex_;
    WifiInfo wifi_info_;
    DeviceRegInfo device_reg_info_;
    
    // 连接状态管理
    std::string connected_device_address_;
    std::chrono::steady_clock::time_point last_connection_time_;
    
    // GATT服务状态
    bool gatt_service_registered_{false};
    bool advertisement_registered_{false};
    std::map<std::string, std::string> characteristic_values_;
};

} // namespace aby_box

#endif // MODULE__BLUETOOTH_MANAGER_HPP_ 