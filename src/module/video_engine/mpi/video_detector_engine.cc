#include "module/video_engine/mpi/video_detector_engine.h"
#include "module/video_engine/mpi/image_capture_interface.h"
#include "module/video_engine/video_engine.hpp"
#include "utils/api_client.h"
#include <cmath>
#include <atomic>
#include <stdexcept>
#include <memory>

// 全局退出标志已在main.cc中定义，这里不需要重复定义

// 猫帧数统计相关变量
static std::atomic<uint32_t> cat_frame_count{0};  // 猫出现的帧数计数
static std::atomic<bool> is_cat_present{false};   // 猫是否在场

// 错误处理相关全局变量
extern std::atomic<aby_box::VideoEngine*> g_video_engine_instance;

MUTEXAUTOLOCK_INIT(ResultMutex);

// 安全的错误报告函数
static void reportVideoError(aby_box::CameraErrorType error_type, const std::string& message) {
  if (auto* engine = g_video_engine_instance.load()) {
    engine->reportCameraError(error_type, message);
  } else {
    printf("Camera Error [%d]: %s\n", static_cast<int>(error_type), message.c_str());
    
    // 如果没有engine实例，直接发送传感器错误状态到服务器
    std::thread([error_type, message]() {
      try {
                 // 构造错误码和错误消息（限制长度以符合数据库列限制）
         int error_code = static_cast<int>(error_type);
         std::string error_message = message;
         if (error_message.length() > 50) {
           error_message = error_message.substr(0, 47) + "...";
         }
        
                 // 根据错误类型添加额外信息（缩短字符串以符合数据库列长度限制）
         std::string additional_info;
         switch (error_type) {
           case aby_box::CameraErrorType::INITIALIZATION_FAILED:
             additional_info = "Init failed";
             break;
           case aby_box::CameraErrorType::SENSOR_DISCONNECTED:
             additional_info = "Sensor disconnected";
             break;
           case aby_box::CameraErrorType::API_CALL_FAILED:
             additional_info = "API call failed";
             break;
           case aby_box::CameraErrorType::MEMORY_ERROR:
             additional_info = "Memory error";
             break;
           case aby_box::CameraErrorType::THREAD_CRASHED:
             additional_info = "Thread crashed";
             break;
           case aby_box::CameraErrorType::UNKNOWN_ERROR:
             additional_info = "Unknown error";
             break;
           default:
             additional_info = "Unspecified error";
             break;
         }
        
        // 调用API上报传感器错误
        bool success = aby_box::APIClient::getInstance().reportSensorError(
          "camera", error_code, error_message, additional_info);
        
        if (success) {
          printf("Camera error successfully reported to server\n");
        } else {
          printf("Failed to report camera error to server\n");
        }
      } catch (const std::exception& e) {
        printf("Exception while reporting camera error to server: %s\n", e.what());
      } catch (...) {
        printf("Unknown exception while reporting camera error to server\n");
      }
    }).detach(); // 使用detach避免阻塞主线程
  }
}

// 静态标志位，用于跟踪camera错误是否已被清除
static std::atomic<bool> camera_error_cleared{false};

// 清除camera错误状态（只执行一次）
static void clearCameraErrorOnce() {
  bool expected = false;
  if (camera_error_cleared.compare_exchange_strong(expected, true)) {
    // 异步清除camera错误状态，避免阻塞主线程
    std::thread([]() {
      try {
        bool success = aby_box::APIClient::getInstance().clearSensorError("camera");
        
        if (success) {
          printf("Camera error status successfully cleared on server\n");
        } else {
          printf("Failed to clear camera error status on server\n");
          // 清除失败时重置标志位，允许下次重试
          std::this_thread::sleep_for(std::chrono::seconds(3000));
          camera_error_cleared.store(false);
        }
      } catch (const std::exception& e) {
        printf("Exception while clearing camera error status: %s\n", e.what());
        // 异常时重置标志位，允许下次重试
        std::this_thread::sleep_for(std::chrono::seconds(3000));
        camera_error_cleared.store(false);
      } catch (...) {
        printf("Unknown exception while clearing camera error status\n");
        // 异常时重置标志位，允许下次重试
        std::this_thread::sleep_for(std::chrono::seconds(3000));
        camera_error_cleared.store(false);
      }
    }).detach();
  }
}

// 检查摄像头线程是否应该继续运行
static bool shouldCameraThreadsContinue() {
  if (bExit) {
    return false; // 全局退出
  }
  
  if (auto* engine = g_video_engine_instance.load()) {
    return engine->isCameraHealthy();
  }
  
  return true; // 如果没有engine实例，默认继续运行
}

// 猫帧数统计相关函数实现
uint32_t get_cat_frame_count() {
  return cat_frame_count.load();
}

void reset_cat_frame_count() {
  cat_frame_count.store(0);
}

void *run_cat_event_listener_thread(void *args) {
  printf("Enter cat event listener thread\n");
  
  // 检查是否应该立即退出（例如VI初始化失败）
  if (!shouldCameraThreadsContinue()) {
    printf("Cat event listener thread exiting immediately due to camera failure\n");
    pthread_exit(NULL);
  }
  
  try {
    // 创建猫事件订阅者
    uorb::SubscriptionData<uorb::msg::cat_event> sub_cat_event;
    
    // 设置polling用于检查猫事件
    struct orb_pollfd poll_fd_cat = {
        .fd = sub_cat_event.handle(), .events = POLLIN, .revents = 0};
    int timeout_ms = 200; // 200ms timeout for polling
    
    while (shouldCameraThreadsContinue()) {
      try {
        // 检查猫事件
        if (0 < orb_poll(&poll_fd_cat, 1, timeout_ms)) {
          if ((poll_fd_cat.revents & POLLIN) && sub_cat_event.Update()) {
            auto cat_event_data = sub_cat_event.get();
            
            if (cat_event_data.event_type == CAT_ENTER) {
              is_cat_present.store(true);
              cat_frame_count.store(0); // 重置帧数计数
            } else if (cat_event_data.event_type == CAT_LEAVE) {
              is_cat_present.store(false);
              // 帧数会在weight_data_listener中使用，这里不重置
            }
          }
        }
        usleep(100000); // 100ms延迟
      } catch (const std::exception& e) {
        reportVideoError(aby_box::CameraErrorType::API_CALL_FAILED, 
                        "Exception in cat event processing: " + std::string(e.what()));
        usleep(1000000); // 发生异常时等待1秒
      }
    }
  } catch (const std::exception& e) {
    reportVideoError(aby_box::CameraErrorType::THREAD_CRASHED, 
                    "Cat event listener thread crashed: " + std::string(e.what()));
  } catch (...) {
    reportVideoError(aby_box::CameraErrorType::THREAD_CRASHED, 
                    "Cat event listener thread crashed with unknown exception");
  }
  
  printf("Exit cat event listener thread\n");
  pthread_exit(NULL);
}

void *run_audio(void *args) {
  // 检查是否应该立即退出（例如VI初始化失败）
  if (!shouldCameraThreadsContinue()) {
    printf("Audio thread exiting immediately due to camera failure\n");
    pthread_exit(NULL);
  }
  
  CVI_S32 s32Ret;
  SAMPLE_TDL_VENC_THREAD_ARG_S *pstArgs = (SAMPLE_TDL_VENC_THREAD_ARG_S *)args;
  CVI_RTSP_CTX *ctx = pstArgs->pstMWContext->pstRtspContext;
  CVI_RTSP_SESSION *session = pstArgs->pstMWContext->pstSession;

  // 检查音频是否已启用
  if (!pstArgs->pstMWContext->bAudioEnabled) {
    printf("Audio is disabled, audio thread exiting\n");
    pthread_exit(NULL);
  }
  
  while (shouldCameraThreadsContinue()) {
    AUDIO_FRAME_S stFrame;
    AEC_FRAME_S stAecFrm;

    memset(&stFrame, 0, sizeof(AUDIO_FRAME_S));

    s32Ret = CVI_AI_GetFrame(0, 0, &stFrame, &stAecFrm, -1);
    if (s32Ret != CVI_SUCCESS) {
      printf("CVI_AI_GetFrame --> none!!\n");
      usleep(10000); // 音频获取失败时等待10ms，避免忙循环
      continue;
    }

    CVI_RTSP_DATA data = {0};
    data.dataPtr[0] = stFrame.u64VirAddr[0];
    data.dataLen[0] = stFrame.u32Len * 2;
    data.blockCnt = 1;

    CVI_RTSP_WriteFrame(ctx, session->audio, &data);
  }
  
  printf("Exit audio thread\n");
  pthread_exit(NULL);
}

void *run_venc(void *args) {
  printf("Enter encoder thread\n");
  
  // 检查是否应该立即退出（例如VI初始化失败）
  if (!shouldCameraThreadsContinue()) {
    printf("Encoder thread exiting immediately due to camera failure\n");
    pthread_exit(NULL);
  }
  
  SAMPLE_TDL_VENC_THREAD_ARG_S *pstArgs = (SAMPLE_TDL_VENC_THREAD_ARG_S *)args;
  
  try {
    VIDEO_FRAME_INFO_S stFrame;
    CVI_S32 s32Ret;
    cvtdl_object_t stObjMeta = {0};

    while (shouldCameraThreadsContinue()) {
      try {
        s32Ret = CVI_VPSS_GetChnFrame(0, 0, &stFrame, 2000);
        if (s32Ret != CVI_SUCCESS) {
          printf("CVI_VPSS_GetChnFrame chn0 failed with %#x\n", s32Ret);
          reportVideoError(aby_box::CameraErrorType::API_CALL_FAILED, 
                          "CVI_VPSS_GetChnFrame failed in encoder with code: 0x" + 
                          std::to_string(s32Ret));
          break;
        } else {
          // CVI_VPSS_GetChnFrame成功，说明camera工作正常，清除错误状态
          clearCameraErrorOnce();
        }

        {
          MutexAutoLock(ResultMutex, lock);
          CVI_TDL_CopyObjectMeta(&g_stObjMeta, &stObjMeta);
        }

        // 根据视频模式控制是否应用灰度处理
        aby_box::VideoEngine* video_engine = g_video_engine_instance.load();
        if (video_engine && video_engine->isGrayModeEnabled()) {
          // 修改视频帧为灰度图像（保留Y平面，去除UV色彩信息）
          VIDEO_FRAME_S *pstVFrame = &stFrame.stVFrame;

          // 只处理UV平面，保留原始Y平面的亮度信息
          // 映射UV平面物理地址到虚拟地址 (对于NV21格式)
          if (pstVFrame->u64PhyAddr[1] != 0) {
            uint8_t *pUVVirAddr = (uint8_t *)CVI_SYS_Mmap(
                pstVFrame->u64PhyAddr[1], pstVFrame->u32Length[1]);

            if (pUVVirAddr != nullptr && pUVVirAddr != (void *)-1) {
              // 将UV平面填充为中性值 (128, 128) 以去除色彩，保持灰度
              memset(pUVVirAddr, 128, pstVFrame->u32Length[1]);

              // 解除UV平面映射
              CVI_SYS_Munmap(pUVVirAddr, pstVFrame->u32Length[1]);
            }
          }
        }

        s32Ret = SAMPLE_TDL_Send_Frame_RTSP(&stFrame, pstArgs->pstMWContext);
        if (s32Ret != CVI_SUCCESS) {
          reportVideoError(aby_box::CameraErrorType::API_CALL_FAILED, 
                          "SAMPLE_TDL_Send_Frame_RTSP failed with code: 0x" + 
                          std::to_string(s32Ret));
        }

      error:
        CVI_TDL_Free(&stObjMeta);
        CVI_VPSS_ReleaseChnFrame(0, 0, &stFrame);
        
        if (s32Ret != CVI_SUCCESS) {
          // 不立即退出，而是报告错误并继续尝试
          usleep(100000); // 等待100ms再重试
        }
        
      } catch (const std::exception& e) {
        reportVideoError(aby_box::CameraErrorType::API_CALL_FAILED, 
                        "Exception in encoder frame processing: " + std::string(e.what()));
        usleep(1000000); // 发生异常时等待1秒
      }
    }
  } catch (const std::exception& e) {
    reportVideoError(aby_box::CameraErrorType::THREAD_CRASHED, 
                    "Encoder thread crashed: " + std::string(e.what()));
  } catch (...) {
    reportVideoError(aby_box::CameraErrorType::THREAD_CRASHED, 
                    "Encoder thread crashed with unknown exception");
  }
  
  printf("Exit encoder thread\n");
  pthread_exit(NULL);
}

void *run_image_capture_thread(void *args) {
  printf("Enter Image Capture thread\n");
  
  // 检查是否应该立即退出（例如VI初始化失败）
  if (!shouldCameraThreadsContinue()) {
    printf("Image capture thread exiting immediately due to camera failure\n");
    pthread_exit(NULL);
  }
  
  auto& queue = ImageCaptureQueue::getInstance();
  
  while (shouldCameraThreadsContinue()) {
    ImageCaptureData data;
    
    if (!queue.popFrame(data)) {
      printf("Image Capture thread: queue is empty\n");
      usleep(100000);
      break; // 队列已关闭
    }
    
    // YUV到RGB转换
    std::vector<uint8_t> rgb_data(data.width * data.height * 3);
    uint8_t *pYAddr = data.pYData;
    uint8_t *pUVAddr = data.pUVData;
    
    for (uint32_t y = 0; y < data.height; y++) {
      for (uint32_t x = 0; x < data.width; x++) {
        // Y分量
        size_t y_idx = y * data.yStride + x;
        if (y_idx >= data.yLength) {
          continue;
        }
        int Y = pYAddr[y_idx];
        
        // UV分量 (NV21格式VUVU交错)
        uint32_t uv_y = y / 2;
        uint32_t uv_x = (x / 2) * 2;
        uint32_t uv_index = uv_y * data.uvStride + uv_x;
        
        if (uv_index + 1 >= data.uvLength) {
          // UV数据不足，使用灰度
          size_t rgb_idx = (y * data.width + x) * 3;
          if (rgb_idx + 2 < rgb_data.size()) {
            rgb_data[rgb_idx] = static_cast<uint8_t>(Y);
            rgb_data[rgb_idx + 1] = static_cast<uint8_t>(Y);
            rgb_data[rgb_idx + 2] = static_cast<uint8_t>(Y);
          }
          continue;
        }
        
        // NV21: VUVU交错
        int V = pUVAddr[uv_index] - 128;
        int U = pUVAddr[uv_index + 1] - 128;
        
        // YUV到RGB转换
        double R_f = Y + 1.402 * V;
        double G_f = Y - 0.344136 * U - 0.714136 * V;
        double B_f = Y + 1.772 * U;
        
        int R = std::max(0, std::min(255, (int)(R_f + 0.5)));
        int G = std::max(0, std::min(255, (int)(G_f + 0.5)));
        int B = std::max(0, std::min(255, (int)(B_f + 0.5)));
        
        size_t rgb_idx = (y * data.width + x) * 3;
        if (rgb_idx + 2 >= rgb_data.size()) {
          continue;
        }
        rgb_data[rgb_idx] = static_cast<uint8_t>(R);
        rgb_data[rgb_idx + 1] = static_cast<uint8_t>(G);
        rgb_data[rgb_idx + 2] = static_cast<uint8_t>(B);
      }
    }
    
    // 调用图像捕获接口
    capture_image_frame(rgb_data.data(), data.width, data.height, 
                        data.score, data.timestamp);
    
    // 释放内存
    if (data.pYData) free(data.pYData);
    if (data.pUVData) free(data.pUVData);
  }
  
  printf("Exit Image Capture thread\n");
  pthread_exit(NULL);
}

void *run_tdl_thread(void *args) {
  printf("Enter TDL thread\n");
  
  // 检查是否应该立即退出（例如VI初始化失败）
  if (!shouldCameraThreadsContinue()) {
    printf("TDL thread exiting immediately due to camera failure\n");
    pthread_exit(NULL);
  }
  
  SAMPLE_TDL_TDL_THREAD_ARG_S *pstTDLArgs = (SAMPLE_TDL_TDL_THREAD_ARG_S *)args;
  
  try {
    VIDEO_FRAME_INFO_S stFrame;
    cvtdl_object_t stObjMeta = {0};
    CVI_S32 s32Ret;
    uint32_t frame_counter = 0;

    // 图像捕获时间限制：每秒最多一次
    static uint64_t last_capture_time = 0;
    static const uint64_t CAPTURE_INTERVAL_US = 1000000; // 1秒 = 1,000,000微秒

    // 创建发布者
    uorb::PublicationData<uorb::msg::camera_object_detection>
        pub_object_detection;

    while (shouldCameraThreadsContinue()) {
      try {
        s32Ret = CVI_VPSS_GetChnFrame(0, VPSS_CHN1, &stFrame, 2000);
        if (s32Ret != CVI_SUCCESS) {
          printf("CVI_VPSS_GetChnFrame failed with %#x\n", s32Ret);
          reportVideoError(aby_box::CameraErrorType::API_CALL_FAILED, 
                          "CVI_VPSS_GetChnFrame failed in TDL thread with code: 0x" + 
                          std::to_string(s32Ret));
          goto get_frame_failed;
        } else {
          // CVI_VPSS_GetChnFrame成功，说明camera工作正常，清除错误状态
          clearCameraErrorOnce();
        }

        s32Ret = pstTDLArgs->inference_func(pstTDLArgs->stTDLHandle, &stFrame,
                                            pstTDLArgs->enOdModelId, &stObjMeta);
        if (s32Ret != CVI_TDL_SUCCESS) {
          printf("inference failed!, ret=%x\n", s32Ret);
          reportVideoError(aby_box::CameraErrorType::API_CALL_FAILED, 
                          "AI inference failed with code: 0x" + std::to_string(s32Ret));
          goto inf_error;
        }

    if (pstTDLArgs->enOdModelId ==
        CVI_TDL_SUPPORTED_MODEL_PERSON_PETS_DETECTION) {
      // 发布每个检测到的目标
      for (uint32_t oid = 0; oid < stObjMeta.size; oid++) {
        auto &data = pub_object_detection.get();

        // 填充基本信息
        data.timestamp = orb_absolute_time_us();
        data.frame_id = frame_counter;
        data.object_type = stObjMeta.info[oid].classes;
        data.object_count = stObjMeta.size;
        data.current_object_index = oid;

        // 填充检测结果
        data.confidence = stObjMeta.info[oid].bbox.score;
        data.bbox_x1 = stObjMeta.info[oid].bbox.x1;
        data.bbox_y1 = stObjMeta.info[oid].bbox.y1;
        data.bbox_x2 = stObjMeta.info[oid].bbox.x2;
        data.bbox_y2 = stObjMeta.info[oid].bbox.y2;

        // 发布数据
        if (!pub_object_detection.Publish()) {
          printf("Failed to publish object detection data\n");
        }

        // 如果检测到猫，进行帧数统计
        if (stObjMeta.info[oid].classes == 0 && stObjMeta.info[oid].bbox.score > 0.49f) {
          if (is_cat_present.load()) {
            uint32_t current_count = cat_frame_count.fetch_add(1) + 1;
          }
          bool is_recording = aby_box::APIClient::getInstance().isRecording();
          uint64_t current_time = data.timestamp;
          
          // 检查时间间隔：只有距离上次捕获超过1秒才进行新的捕获
          if (is_recording && (current_time - last_capture_time >= CAPTURE_INTERVAL_US)) {
            
            // 性能优化：在映射物理地址之前先检查置信度是否应该处理
            // 只处理置信度最高的三个数据，跳过重复和低置信度数据，节省CPU
            if (!ImageCaptureQueue::getInstance().shouldProcessConfidence(stObjMeta.info[oid].bbox.score)) {
              continue; // 跳过当前帧的处理，直接处理下一个目标
            }
            
            VIDEO_FRAME_S *pstVFrame = &stFrame.stVFrame;
            
            // 映射Y和UV平面物理地址到虚拟地址
            uint8_t *pYVirAddr = (uint8_t *)CVI_SYS_Mmap(
                pstVFrame->u64PhyAddr[0], pstVFrame->u32Length[0]);
            uint8_t *pUVVirAddr = (uint8_t *)CVI_SYS_Mmap(
                pstVFrame->u64PhyAddr[1], pstVFrame->u32Length[1]);

            if (pYVirAddr != nullptr && pYVirAddr != (void *)-1 &&
                pUVVirAddr != nullptr && pUVVirAddr != (void *)-1) {
              
              // 准备图像捕获数据
              ImageCaptureData captureData;
              captureData.width = pstVFrame->u32Width;
              captureData.height = pstVFrame->u32Height;
              captureData.yStride = pstVFrame->u32Stride[0];
              captureData.uvStride = pstVFrame->u32Stride[1] ? pstVFrame->u32Stride[1] : pstVFrame->u32Stride[0];
              captureData.yLength = pstVFrame->u32Length[0];
              captureData.uvLength = pstVFrame->u32Length[1];
              captureData.score = stObjMeta.info[oid].bbox.score;
              captureData.timestamp = data.timestamp;
              
              // 分配内存并拷贝Y数据
              captureData.pYData = (uint8_t*)malloc(captureData.yLength);
              if (captureData.pYData) {
                memcpy(captureData.pYData, pYVirAddr, captureData.yLength);
              }
              
              // 分配内存并拷贝UV数据
              captureData.pUVData = (uint8_t*)malloc(captureData.uvLength);
              if (captureData.pUVData) {
                memcpy(captureData.pUVData, pUVVirAddr, captureData.uvLength);
              }
              
              // 只有在成功分配和拷贝数据后才推送到队列
              if (captureData.pYData && captureData.pUVData) {
                ImageCaptureQueue::getInstance().pushFrame(captureData);
                // 更新上次捕获时间
                last_capture_time = current_time;
              } else {
                // 内存分配失败，清理已分配的内存
                if (captureData.pYData) free(captureData.pYData);
                if (captureData.pUVData) free(captureData.pUVData);
              }
            }
            
            // 解除映射
            CVI_SYS_Munmap(pYVirAddr, pstVFrame->u32Length[0]);
            CVI_SYS_Munmap(pUVVirAddr, pstVFrame->u32Length[1]);
          }
        }
      }
      frame_counter++;
    }

    {
      MutexAutoLock(ResultMutex, lock);
      CVI_TDL_CopyObjectMeta(&stObjMeta, &g_stObjMeta);
    }

      inf_error:
        CVI_VPSS_ReleaseChnFrame(0, 1, &stFrame);
      get_frame_failed:
        CVI_TDL_Free(&stObjMeta);
        
        if (s32Ret != CVI_SUCCESS) {
          // 不立即退出，而是报告错误并继续尝试
          usleep(100000); // 等待100ms再重试
        }
        
      } catch (const std::exception& e) {
        reportVideoError(aby_box::CameraErrorType::API_CALL_FAILED, 
                        "Exception in TDL frame processing: " + std::string(e.what()));
        // 清理可能的资源
        try {
          CVI_TDL_Free(&stObjMeta);
        } catch (...) {
          // 忽略清理过程中的异常
        }
        usleep(1000000); // 发生异常时等待1秒
      }
    }
  } catch (const std::exception& e) {
    reportVideoError(aby_box::CameraErrorType::THREAD_CRASHED, 
                    "TDL thread crashed: " + std::string(e.what()));
  } catch (...) {
    reportVideoError(aby_box::CameraErrorType::THREAD_CRASHED, 
                    "TDL thread crashed with unknown exception");
  }

  printf("Exit TDL thread\n");
  pthread_exit(NULL);
}

int launch_main() {
  printf("Starting camera initialization with error handling\n");
  
  // 将重要变量定义在try块之前，确保catch块中可以访问
  SAMPLE_TDL_MW_CONFIG_S stMWConfig;
  memset(&stMWConfig, 0, sizeof(SAMPLE_TDL_MW_CONFIG_S));
  SAMPLE_TDL_MW_CONTEXT stMWContext = {0};
  cvitdl_handle_t stTDLHandle = NULL;
  cvitdl_service_handle_t stServiceHandle = NULL;
  SAMPLE_TDL_VENC_THREAD_ARG_S args;
  ODInferenceFunc inference_func;
  CVI_TDL_SUPPORTED_MODEL_E enOdModelId = CVI_TDL_SUPPORTED_MODEL_PERSON_PETS_DETECTION;
  SAMPLE_TDL_TDL_THREAD_ARG_S ai_args;
  pthread_t stVencThread, stTDLThread, stAudioThread, stImageCaptureThread, stCatEventThread;
  CVI_S32 s32Ret;
  
  try {

    // 初始化配置
    s32Ret = SAMPLE_TDL_Get_VI_Config(&stMWConfig.stViConfig);
    if (s32Ret != CVI_SUCCESS || stMWConfig.stViConfig.s32WorkingViNum <= 0) {
      printf("Failed to get sensor information\n");
      reportVideoError(aby_box::CameraErrorType::INITIALIZATION_FAILED, 
                      "Failed to get sensor configuration");
      return -1;
    }

    // Get VI size
    PIC_SIZE_E enPicSize;
    s32Ret = SAMPLE_COMM_VI_GetSizeBySensor(
        stMWConfig.stViConfig.astViInfo[0].stSnsInfo.enSnsType, &enPicSize);
    if (s32Ret != CVI_SUCCESS) {
      printf("Cannot get sensor size\n");
      reportVideoError(aby_box::CameraErrorType::SENSOR_DISCONNECTED, 
                      "Cannot get sensor size by type");
      return -1;
    }

    SIZE_S stSensorSize;
    s32Ret = SAMPLE_COMM_SYS_GetPicSize(enPicSize, &stSensorSize);
    if (s32Ret != CVI_SUCCESS) {
      printf("Cannot get sensor size\n");
      reportVideoError(aby_box::CameraErrorType::SENSOR_DISCONNECTED, 
                      "Cannot get picture size from sensor");
      return -1;
    }

  // Setup frame size of video encoder to 1080p
  SIZE_S stVencSize = {
      .u32Width = 1280,
      .u32Height = 720,
  };

  stMWConfig.stVBPoolConfig.u32VBPoolCount = 3;

  // VBPool 0 for VPSS Grp0 Chn0
  stMWConfig.stVBPoolConfig.astVBPoolSetup[0].enFormat = VI_PIXEL_FORMAT;
  stMWConfig.stVBPoolConfig.astVBPoolSetup[0].u32BlkCount = 5;
  stMWConfig.stVBPoolConfig.astVBPoolSetup[0].u32Height =
      stSensorSize.u32Height;
  stMWConfig.stVBPoolConfig.astVBPoolSetup[0].u32Width = stSensorSize.u32Width;
  stMWConfig.stVBPoolConfig.astVBPoolSetup[0].bBind = true;
  stMWConfig.stVBPoolConfig.astVBPoolSetup[0].u32VpssChnBinding = VPSS_CHN0;
  stMWConfig.stVBPoolConfig.astVBPoolSetup[0].u32VpssGrpBinding = (VPSS_GRP)0;

  // VBPool 1 for VPSS Grp0 Chn1
  stMWConfig.stVBPoolConfig.astVBPoolSetup[1].enFormat = VI_PIXEL_FORMAT;
  stMWConfig.stVBPoolConfig.astVBPoolSetup[1].u32BlkCount = 5;
  stMWConfig.stVBPoolConfig.astVBPoolSetup[1].u32Height = stVencSize.u32Height;
  stMWConfig.stVBPoolConfig.astVBPoolSetup[1].u32Width = stVencSize.u32Width;
  stMWConfig.stVBPoolConfig.astVBPoolSetup[1].bBind = true;
  stMWConfig.stVBPoolConfig.astVBPoolSetup[1].u32VpssChnBinding = VPSS_CHN1;
  stMWConfig.stVBPoolConfig.astVBPoolSetup[1].u32VpssGrpBinding = (VPSS_GRP)0;

  // VBPool 2 for TDL preprocessing
  stMWConfig.stVBPoolConfig.astVBPoolSetup[2].enFormat =
      PIXEL_FORMAT_BGR_888_PLANAR;
  stMWConfig.stVBPoolConfig.astVBPoolSetup[2].u32BlkCount = 3;
  stMWConfig.stVBPoolConfig.astVBPoolSetup[2].u32Height = 720;
  stMWConfig.stVBPoolConfig.astVBPoolSetup[2].u32Width = 1280;
  stMWConfig.stVBPoolConfig.astVBPoolSetup[2].bBind = false;

  // Setup VPSS Grp0
  stMWConfig.stVPSSPoolConfig.u32VpssGrpCount = 1;
  stMWConfig.stVPSSPoolConfig.stVpssMode.aenInput[0] = VPSS_INPUT_MEM;
  stMWConfig.stVPSSPoolConfig.stVpssMode.enMode = VPSS_MODE_DUAL;
  stMWConfig.stVPSSPoolConfig.stVpssMode.ViPipe[0] = 0;
  stMWConfig.stVPSSPoolConfig.stVpssMode.aenInput[1] = VPSS_INPUT_ISP;
  stMWConfig.stVPSSPoolConfig.stVpssMode.ViPipe[1] = 0;

  SAMPLE_TDL_VPSS_CONFIG_S *pstVpssConfig =
      &stMWConfig.stVPSSPoolConfig.astVpssConfig[0];
  pstVpssConfig->bBindVI = true;

  // Assign device 1 to VPSS Grp0, because device1 has 3 outputs in dual mode.
  VPSS_GRP_DEFAULT_HELPER2(&pstVpssConfig->stVpssGrpAttr, stSensorSize.u32Width,
                           stSensorSize.u32Height, VI_PIXEL_FORMAT, 1);
  pstVpssConfig->u32ChnCount = 2;
  pstVpssConfig->u32ChnBindVI = 0;
  VPSS_CHN_DEFAULT_HELPER(&pstVpssConfig->astVpssChnAttr[0],
                          stVencSize.u32Width, stVencSize.u32Height,
                          VI_PIXEL_FORMAT, true);
  VPSS_CHN_DEFAULT_HELPER(&pstVpssConfig->astVpssChnAttr[1],
                          stVencSize.u32Width, stVencSize.u32Height,
                          VI_PIXEL_FORMAT, true);

  // Get default VENC configurations
  SAMPLE_TDL_Get_Input_Config(&stMWConfig.stVencConfig.stChnInputCfg);
  stMWConfig.stVencConfig.u32FrameWidth = stVencSize.u32Width;
  stMWConfig.stVencConfig.u32FrameHeight = stVencSize.u32Height;

  // Get default RTSP configurations
  SAMPLE_TDL_Get_RTSP_Config(&stMWConfig.stRTSPConfig.stRTSPConfig);

  // 设置RTSP推流地址
  stMWContext.streamer.outputUrl = "rtsp://192.168.42.2:8554/live";

    s32Ret = SAMPLE_TDL_Init_WM(&stMWConfig, &stMWContext);
    if (s32Ret != CVI_SUCCESS) {
      printf("init middleware failed! ret=%x\n", s32Ret);
      reportVideoError(aby_box::CameraErrorType::INITIALIZATION_FAILED, 
                      "Middleware initialization failed with code: 0x" + std::to_string(s32Ret));
      // VI初始化失败时，立即设置退出标志并返回，避免创建任何线程
      bExit = true;
      return -1;
    }

    // 创建TDL handle
    s32Ret = CVI_TDL_CreateHandle2(&stTDLHandle, 1, 0);
    if (s32Ret != CVI_SUCCESS) {
      reportVideoError(aby_box::CameraErrorType::INITIALIZATION_FAILED, 
                      "TDL handle creation failed with code: 0x" + std::to_string(s32Ret));
      goto create_tdl_fail;
    }

    s32Ret = CVI_TDL_SetVBPool(stTDLHandle, 0, 2);
    if (s32Ret != CVI_SUCCESS) {
      reportVideoError(aby_box::CameraErrorType::MEMORY_ERROR, 
                      "TDL VB pool setup failed with code: 0x" + std::to_string(s32Ret));
      goto create_service_fail;
    }

    CVI_TDL_SetVpssTimeout(stTDLHandle, 1000);

    s32Ret = CVI_TDL_Service_CreateHandle(&stServiceHandle, stTDLHandle);
    if (s32Ret != CVI_SUCCESS) {
      reportVideoError(aby_box::CameraErrorType::INITIALIZATION_FAILED, 
                      "TDL service handle creation failed with code: 0x" + std::to_string(s32Ret));
      goto create_service_fail;
    }

    // 获取模型信息
    get_od_model_info("yolov8-person-pets", &enOdModelId, &inference_func);

    s32Ret = CVI_TDL_OpenModel(stTDLHandle, enOdModelId,
                               "/mnt/data/pet_det_640x384.cvimodel");
    if (s32Ret != CVI_SUCCESS) {
      reportVideoError(aby_box::CameraErrorType::INITIALIZATION_FAILED, 
                      "AI model loading failed with code: 0x" + std::to_string(s32Ret));
      goto setup_tdl_fail;
    }

    // 初始化图像捕获管理器
    if (init_image_capture_manager() != 0) {
      printf("Failed to initialize image capture manager\n");
      reportVideoError(aby_box::CameraErrorType::INITIALIZATION_FAILED, 
                      "Image capture manager initialization failed");
      goto setup_tdl_fail;
    }

    printf("All camera initialization completed successfully, creating threads\n");

    // 设置参数
    args.pstMWContext = &stMWContext;
    args.stServiceHandle = stServiceHandle;
    args.enOdModelId = enOdModelId;

    ai_args.enOdModelId = enOdModelId;
    ai_args.inference_func = inference_func;
    ai_args.stTDLHandle = stTDLHandle;

    // 只有在所有初始化都成功后才创建线程
    printf("Creating camera threads\n");
    pthread_create(&stVencThread, NULL, run_venc, &args);
    pthread_setname_np(stVencThread, "aby_rtsp_venc");

    pthread_create(&stTDLThread, NULL, run_tdl_thread, &ai_args);
    pthread_setname_np(stTDLThread, "aby_rtsp_tdl");

    pthread_create(&stAudioThread, NULL, run_audio, &args);
    pthread_setname_np(stAudioThread, "aby_rtsp_audio");

    pthread_create(&stImageCaptureThread, NULL, run_image_capture_thread, NULL);
    pthread_setname_np(stImageCaptureThread, "aby_img_capture");

    pthread_create(&stCatEventThread, NULL, run_cat_event_listener_thread, NULL);
    pthread_setname_np(stCatEventThread, "aby_cat_event");

    printf("All camera threads created successfully\n");

    // 等待线程结束
    pthread_join(stVencThread, NULL);
    pthread_join(stTDLThread, NULL);
    pthread_join(stAudioThread, NULL);
    pthread_join(stCatEventThread, NULL);
    
    // 关闭图像捕获队列并等待线程结束
    ImageCaptureQueue::getInstance().shutdown();
    pthread_join(stImageCaptureThread, NULL);
    
    // 清理资源
    destroy_image_capture_manager();
    
    printf("Camera launch_main completed successfully - all threads exited\n");
    return 0;

  setup_tdl_fail:
    printf("Cleaning up TDL resources due to initialization failure\n");
    if (stServiceHandle) {
      CVI_TDL_Service_DestroyHandle(stServiceHandle);
    }
  create_service_fail:
    if (stTDLHandle) {
      CVI_TDL_DestroyHandle(stTDLHandle);
    }
  create_tdl_fail:
    SAMPLE_TDL_Destroy_MW(&stMWContext);
    bExit = true;
    printf("Camera initialization failed, exiting launch_main\n");
    return -1;

  } catch (const std::exception& e) {
    reportVideoError(aby_box::CameraErrorType::UNKNOWN_ERROR, 
                    "Exception in launch_main: " + std::string(e.what()));
    printf("Exception in camera initialization: %s\n", e.what());
    
    // 确保设置退出标志
    bExit = true;
    
    // 尝试清理资源
    if (stServiceHandle) {
      CVI_TDL_Service_DestroyHandle(stServiceHandle);
    }
    if (stTDLHandle) {
      CVI_TDL_DestroyHandle(stTDLHandle);
    }
    SAMPLE_TDL_Destroy_MW(&stMWContext);
    
    return -1;
  } catch (...) {
    reportVideoError(aby_box::CameraErrorType::UNKNOWN_ERROR, 
                    "Unknown exception in launch_main");
    printf("Unknown exception in camera initialization\n");
    
    // 确保设置退出标志
    bExit = true;
    
    // 尝试清理资源
    try {
      if (stServiceHandle) {
        CVI_TDL_Service_DestroyHandle(stServiceHandle);
      }
      if (stTDLHandle) {
        CVI_TDL_DestroyHandle(stTDLHandle);
      }
      SAMPLE_TDL_Destroy_MW(&stMWContext);
    } catch (...) {
      // 忽略清理过程中的异常
    }
    
    return -1;
  }
}