// Copyright (c) 2024 animsi Technology Co., Ltd. All rights reserved.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

#include "module/bluetooth_manager.hpp"
#include "utils/utils.hpp"
#include "utils/config_manager.hpp"
#include <nlohmann/json.hpp>
#include <sstream>
#include <fstream>
#include <iostream>
#include <unistd.h>
#include <cstdlib>
#include <cstring>
#include <cstdint>

using json = nlohmann::json;

namespace aby_box {

// 广告路径定义
constexpr const char* ADVERTISEMENT_PATH = "/com/animsi/abybox/advertisement";

// DBus对象路径消息处理函数
static DBusHandlerResult dbus_message_handler(DBusConnection* connection,
                                             DBusMessage* message,
                                             void* user_data) {
    BluetoothManager* manager = static_cast<BluetoothManager*>(user_data);
    if (manager) {
        manager->handle_gatt_message(message);
        return DBUS_HANDLER_RESULT_HANDLED;
    }
    return DBUS_HANDLER_RESULT_NOT_YET_HANDLED;
}

// DBus对象路径虚函数表
DBusObjectPathVTable BluetoothManager::dbus_vtable_ = {
    nullptr,                // unregister_function
    dbus_message_handler,   // message_function
    nullptr,                // dbus_internal_pad1
    nullptr                 // dbus_internal_pad2
};

BluetoothManager::BluetoothManager(const std::string &module_name)
    : BaseModule(module_name) {
    log_handler_->info("BluetoothManager created");
}

BluetoothManager::~BluetoothManager() {
    cleanup_bluetooth();
}

bool BluetoothManager::init() {
    log_handler_->info("Initializing BluetoothManager");
    

    
    return true;
}

bool BluetoothManager::start() {
    // 初始化蓝牙
    if (!init_bluetooth()) {
        log_handler_->error("Failed to initialize bluetooth");
        return false;
    }
    if (is_running_) {
        log_handler_->warn("BluetoothManager is already running");
        return true;
    }
    
    is_running_ = true;
    
    // 启动主线程
    main_thread_ = std::make_unique<std::thread>(&BluetoothManager::bluetooth_main_loop, this);
    pthread_setname_np(main_thread_->native_handle(), "bt_main");
    
    log_handler_->info("BluetoothManager started");
    return true;
}

bool BluetoothManager::stop() {
    if (!is_running_) {
        return true;
    }
    
    log_handler_->info("Stopping BluetoothManager");
    is_running_ = false;
    
    // 停止广播
    stop_advertising();
    
    return true;
}

void BluetoothManager::join() {
    if (main_thread_ && main_thread_->joinable()) {
        main_thread_->join();
    }
    
    cleanup_bluetooth();
}

WifiInfo BluetoothManager::get_wifi_info() const {
    std::lock_guard<std::mutex> lock(wifi_info_mutex_);
    return wifi_info_;
}

DeviceRegInfo BluetoothManager::get_device_reg_info() const {
    std::lock_guard<std::mutex> lock(device_reg_info_mutex_);
    return device_reg_info_;
}

bool BluetoothManager::init_bluetooth() {
    // 禁用经典蓝牙
    if (!disable_classic_bluetooth()) {
        log_handler_->error("Failed to disable classic bluetooth");
        return false;
    }
    
    // 启用BLE模式
    if (!enable_ble_mode()) {
        log_handler_->error("Failed to enable BLE mode");
        return false;
    }
    
    // 连接到DBus
    if (!connect_to_dbus()) {
        log_handler_->error("Failed to connect to DBus");
        return false;
    }
    
    // 设置GATT服务器
    if (!setup_gatt_server()) {
        log_handler_->error("Failed to setup GATT server");
        return false;
    }
    
    return true;
}

void BluetoothManager::cleanup_bluetooth() {
    stop_advertising();
    
    if (gatt_service_registered_) {
        // 注销GATT应用程序
        call_dbus_method(BLUEZ_SERVICE, config_.adapter_path, 
                        BLUEZ_GATT_MANAGER_INTERFACE, "UnregisterApplication");
        gatt_service_registered_ = false;
    }
    
    // 注销DBus对象路径
    unregister_dbus_object_paths();
    
    disconnect_from_dbus();
}

void BluetoothManager::unregister_dbus_object_paths() {
    if (!dbus_connection_) {
        return;
    }
    
    std::lock_guard<std::mutex> lock(dbus_mutex_);
    
    // 注销所有DBus对象路径
    std::vector<std::string> paths = {
        "/com/animsi/abybox",
        GATT_SERVICE_PATH,
        WIFI_CHAR_PATH,
        DEVICE_REG_CHAR_PATH,
        ADVERTISEMENT_PATH
    };
    
    for (const auto& path : paths) {
        if (dbus_connection_unregister_object_path(dbus_connection_, path.c_str())) {
            log_handler_->info("Unregistered DBus object path: {}", path);
        } else {
            log_handler_->warn("Failed to unregister DBus object path: {}", path);
        }
    }
}

bool BluetoothManager::disable_classic_bluetooth() {
    log_handler_->info("Disabling classic bluetooth");
    
    // 嵌入式命令：禁用经典蓝牙
    if (!execute_disable_classic_bt()) {
        log_handler_->error("Failed to execute disable classic bluetooth commands");
        return false;
    }
    
    log_handler_->info("Classic bluetooth disabled successfully");
    return true;
}

bool BluetoothManager::enable_ble_mode() {
    log_handler_->info("Enabling BLE mode");
    
    // 嵌入式命令：启用BLE模式
    if (!execute_enable_ble()) {
        log_handler_->error("Failed to execute enable BLE commands");
        return false;
    }
    
    log_handler_->info("BLE mode enabled successfully");
    return true;
}

bool BluetoothManager::connect_to_dbus() {
    DBusError error;
    dbus_error_init(&error);
    
    dbus_connection_ = dbus_bus_get(DBUS_BUS_SYSTEM, &error);
    if (dbus_error_is_set(&error)) {
        log_handler_->error("Failed to connect to system bus: {}", error.message);
        dbus_error_free(&error);
        return false;
    }
    
    if (!dbus_connection_) {
        log_handler_->error("DBus connection is null");
        return false;
    }
    
    log_handler_->info("Connected to DBus successfully");
    return true;
}

void BluetoothManager::disconnect_from_dbus() {
    if (dbus_connection_) {
        dbus_connection_unref(dbus_connection_);
        dbus_connection_ = nullptr;
    }
}

bool BluetoothManager::setup_gatt_server() {
    log_handler_->info("Setting up GATT server");
    
    // 设置DBus对象路径
    if (!setup_dbus_object_paths()) {
        log_handler_->error("Failed to setup DBus object paths");
        return false;
    }
    
    // 注册GATT应用程序
    if (!register_gatt_application()) {
        log_handler_->error("Failed to register GATT application");
        return false;
    }
    
    // 注册GATT服务
    if (!register_gatt_service()) {
        log_handler_->error("Failed to register GATT service");
        return false;
    }
    
    // 注册特征值
    if (!register_characteristics()) {
        log_handler_->error("Failed to register characteristics");
        return false;
    }

    // 设置广告对象
    if (!setup_advertisement_object()) {
        log_handler_->error("Failed to setup advertisement object");
        return false;
    }

    log_handler_->info("GATT server setup completed");
    return true;
}

bool BluetoothManager::setup_dbus_object_paths() {
    log_handler_->info("Setting up DBus object paths");
    
    if (!dbus_connection_) {
        log_handler_->error("DBus connection not available");
        return false;
    }
    
    // 注册应用程序对象路径
    if (!register_dbus_object_path("/com/animsi/abybox")) {
        log_handler_->error("Failed to register application object path");
        return false;
    }
    
    // 注册服务对象路径
    if (!register_dbus_object_path(GATT_SERVICE_PATH)) {
        log_handler_->error("Failed to register service object path");
        return false;
    }
    
    // 注册特征值对象路径
    if (!register_dbus_object_path(WIFI_CHAR_PATH)) {
        log_handler_->error("Failed to register WiFi characteristic object path");
        return false;
    }
    
    if (!register_dbus_object_path(DEVICE_REG_CHAR_PATH)) {
        log_handler_->error("Failed to register device registration characteristic object path");
        return false;
    }

    // 注册广告对象路径
    if (!register_dbus_object_path(ADVERTISEMENT_PATH)) {
        log_handler_->error("Failed to register advertisement object path");
        return false;
    }

    log_handler_->info("DBus object paths setup completed");
    return true;
}

bool BluetoothManager::register_gatt_application() {
    log_handler_->info("Registering GATT application");
    
    if (!dbus_connection_) {
        log_handler_->error("DBus connection not available");
        return false;
    }
    
    // 首先执行基础的蓝牙命令设置
    if (!execute_start_gatt_server()) {
        log_handler_->error("Failed to start GATT server");
        return false;
    }
    
    // 减少等待时间
    std::this_thread::sleep_for(std::chrono::milliseconds(500));
    
    // 添加消息过滤器处理GATT相关消息
    if (!dbus_connection_add_filter(dbus_connection_, 
                                   reinterpret_cast<DBusHandleMessageFunction>(gatt_message_filter), 
                                   this, nullptr)) {
        log_handler_->error("Failed to add D-Bus message filter");
        return false;
    }
    
    // 简化GATT应用注册，减少重试
    const int max_retries = 2;  // 减少重试次数
    const int timeout_ms = 5000;  // 减少超时时间
    
    for (int retry = 0; retry < max_retries; ++retry) {
        if (retry > 0) {
            std::this_thread::sleep_for(std::chrono::milliseconds(1000)); // 减少重试等待时间
        }
        
        // 创建RegisterApplication方法调用
        DBusMessage* message = dbus_message_new_method_call("org.bluez", "/org/bluez/hci0",
                                               "org.bluez.GattManager1", "RegisterApplication");
        if (!message) {
            log_handler_->error("Failed to create RegisterApplication message");
            continue;
        }
        
        // 设置参数
        DBusMessageIter iter, dict_iter;
        dbus_message_iter_init_append(message, &iter);
        
        // 第一个参数：应用程序路径
        const char* app_path = "/com/animsi/abybox";
        dbus_message_iter_append_basic(&iter, DBUS_TYPE_OBJECT_PATH, &app_path);
        
        // 第二个参数：选项字典（空）
        dbus_message_iter_open_container(&iter, DBUS_TYPE_ARRAY, "{sv}", &dict_iter);
        dbus_message_iter_close_container(&iter, &dict_iter);
        
        // 发送消息并等待回复
        DBusError error;
        dbus_error_init(&error);
        DBusMessage* reply = dbus_connection_send_with_reply_and_block(dbus_connection_, message, timeout_ms, &error);
        
        bool success = false;
        if (!dbus_error_is_set(&error) && reply) {
            log_handler_->info("GATT application registered with BlueZ successfully");
            success = true;
        } else if (dbus_error_is_set(&error)) {
            std::string error_msg = error.message;
            std::string error_name = error.name ? error.name : "Unknown";
            
            // 如果错误是"Already Exists"，说明应用已经注册，视为成功
            if (dbus_error_has_name(&error, "org.bluez.Error.AlreadyExists") ||
                error_msg.find("Already Exists") != std::string::npos) {
                log_handler_->info("GATT application already registered - treating as success");
                success = true;
            } else {
                log_handler_->warn("GATT registration attempt {} failed: {} ({})", retry + 1, error_msg, error_name);
            }
            dbus_error_free(&error);
        }
        
        dbus_message_unref(message);
        if (reply) {
            dbus_message_unref(reply);
        }
        
        if (success) {
            gatt_service_registered_ = true;
            log_handler_->info("GATT application registered successfully");
            return true;
        }
    }
    
    log_handler_->error("Failed to register GATT application after {} attempts", max_retries);
    return false;
}

bool BluetoothManager::register_gatt_service() {
    log_handler_->info("Registering GATT service with UUID: {}", DEVICE_SERVICE_UUID);
    return true;
}

bool BluetoothManager::register_characteristics() {
    log_handler_->info("Registering GATT characteristics");
    
    // 注册WiFi信息特征值
    log_handler_->info("Registering WiFi characteristic with UUID: {}", WIFI_CHARACTERISTIC_UUID);
    characteristic_values_[WIFI_CHAR_PATH] = "{}"; // 初始空JSON
    
    // 设置WiFi特征值的初始数据
    WifiInfo default_wifi;
    json wifi_json;
    wifi_json["ssid"] = default_wifi.ssid;
    wifi_json["password"] = "";
    wifi_json["is_valid"] = default_wifi.is_valid;
    characteristic_values_[WIFI_CHAR_PATH] = wifi_json.dump();
    
    // 注册设备注册信息特征值
    log_handler_->info("Registering Device registration characteristic with UUID: {}", DEVICE_REG_CHARACTERISTIC_UUID);
    
    // 设置设备注册特征值的初始数据
    DeviceRegInfo default_reg;
    json reg_json;
    reg_json["user_id"] = default_reg.user_id;
    reg_json["is_valid"] = default_reg.is_valid;
    characteristic_values_[DEVICE_REG_CHAR_PATH] = reg_json.dump();
    
    log_handler_->info("GATT characteristics registered with UUIDs: WiFi={}, DeviceReg={}", 
                      WIFI_CHARACTERISTIC_UUID, DEVICE_REG_CHARACTERISTIC_UUID);
    
    return true;
}

bool BluetoothManager::start_advertising() {
    if (is_advertising_) {
        log_handler_->warn("Already advertising");
        return true;
    }

    log_handler_->info("Starting BLE advertising");

    // 首先设置基本的蓝牙状态
    std::string cmd = "echo 'power on' | bluetoothctl && "
                     "echo 'discoverable on' | bluetoothctl && "
                     "echo 'pairable off' | bluetoothctl";

    std::string result = execute_command(cmd);

    if (result.find("Failed") != std::string::npos) {
        log_handler_->error("Failed to setup bluetooth for advertising");
        return false;
    }

    // 注册广告对象
    if (!register_advertisement()) {
        log_handler_->error("Failed to register advertisement");
        return false;
    }

    is_advertising_ = true;
    log_handler_->info("BLE advertising started successfully");
    return true;
}

bool BluetoothManager::stop_advertising() {
    if (!is_advertising_) {
        return true;
    }

    log_handler_->info("Stopping BLE advertising");

    // 注销广告对象
    unregister_advertisement();

    std::string cmd = "echo 'advertise off' | bluetoothctl";
    execute_command(cmd);

    is_advertising_ = false;
    log_handler_->info("BLE advertising stopped");
    return true;
}

void BluetoothManager::bluetooth_main_loop() {
    log_handler_->info("Starting bluetooth main loop");
    
    // 启动广播
    start_advertising();
    
    int connection_check_counter = 0;
    
    // 主循环：处理DBus消息和连接监控
    while (is_running_) {
        // 处理DBus消息 - 降低频率
        {
            std::lock_guard<std::mutex> lock(dbus_mutex_);
            if (dbus_connection_) {
                dbus_connection_read_write_dispatch(dbus_connection_, 200);
            }
        }
        
        // 大幅降低连接监控频率：每5秒检查一次而不是每100ms
        if (++connection_check_counter >= 25) {  // 25 * 200ms = 5秒
            connection_monitor_loop();
            connection_check_counter = 0;
        }
        
        std::this_thread::sleep_for(std::chrono::milliseconds(200));
    }
    
    log_handler_->info("Bluetooth main loop ended");
}

void BluetoothManager::connection_monitor_loop() {
    // 使用轻量级方式检查蓝牙适配器状态
    // 直接检查hci接口状态，避免使用bluetoothctl
    static int detailed_check_counter = 0;
    
    // 每12次检查（约1分钟）才进行一次详细检查
    if (++detailed_check_counter < 12) {
        // 简单检查：只检查HCI接口是否活跃
        std::string result = execute_command("hciconfig hci0 2>/dev/null | grep -q 'UP RUNNING' && echo 'up' || echo 'down'");
        bool adapter_up = (result.find("up") != std::string::npos);
        
        // 如果适配器状态异常，重启广播
        if (!adapter_up && is_advertising_) {
            is_advertising_ = false;
            if (config_.auto_advertise && is_running_) {
                start_advertising();
            }
        }
        return;
    }
    
    detailed_check_counter = 0;
    
    // 详细检查：检查实际连接状态（频率很低）
    std::string result = execute_command("timeout 2 bluetoothctl show 2>/dev/null | grep -q 'Discovering: yes\\|Connected: yes' && echo 'active' || echo 'inactive'");
    bool currently_connected = (result.find("active") != std::string::npos);
    
    if (currently_connected != is_connected_) {
        is_connected_ = currently_connected;
        if (is_connected_) {
            last_connection_time_ = std::chrono::steady_clock::now();
        } else {
            // 重新启动广播
            if (config_.auto_advertise && is_running_) {
                start_advertising();
            }
        }
    }
}

// 优化的GATT消息过滤器，只处理必要的消息
DBusHandlerResult BluetoothManager::gatt_message_filter(DBusConnection* connection, 
                                                        DBusMessage* message, 
                                                        void* user_data) {
    // 快速检查消息类型，只处理方法调用
    if (dbus_message_get_type(message) != DBUS_MESSAGE_TYPE_METHOD_CALL) {
        return DBUS_HANDLER_RESULT_NOT_YET_HANDLED;
    }
    
    const char* path = dbus_message_get_path(message);
    if (!path) {
        return DBUS_HANDLER_RESULT_NOT_YET_HANDLED;
    }
    
    // 只处理我们的对象路径
    std::string path_str(path);
    if (path_str.find("/com/animsi/abybox") != 0) {
        return DBUS_HANDLER_RESULT_NOT_YET_HANDLED;
    }
    
    BluetoothManager* manager = static_cast<BluetoothManager*>(user_data);
    if (manager) {
        manager->handle_gatt_message(message);
        return DBUS_HANDLER_RESULT_HANDLED;
    }
    
    return DBUS_HANDLER_RESULT_NOT_YET_HANDLED;
}


void BluetoothManager::handle_gatt_message(DBusMessage* message) {
    if (!message) {
        return;
    }

    const char* interface = dbus_message_get_interface(message);
    const char* member = dbus_message_get_member(message);
    const char* path = dbus_message_get_path(message);

    if (!interface || !member || !path) {
        return;
    }

    DBusMessage* reply = nullptr;
    std::string interface_str(interface);
    std::string member_str(member);
    std::string path_str(path);

    try {
        // 处理Properties接口 (参考示例代码的实现)
        if (interface_str == "org.freedesktop.DBus.Properties") {
            if (member_str == "GetAll") {
                handle_properties_get_all(message);
                return;
            } else if (member_str == "Get") {
                handle_properties_get(message);
                return;
            }
        }
        // 处理GATT特征接口
        else if (interface_str == "org.bluez.GattCharacteristic1") {
            if (member_str == "ReadValue") {
                handle_characteristic_read_value(message);
                return;
            } else if (member_str == "WriteValue") {
                handle_characteristic_write_value(message);
                return;
            }
        }
        // 处理GATT服务接口
        else if (interface_str == "org.bluez.GattService1") {
            log_handler_->debug("GattService1 method call: {}", member_str);
        }
        // 处理ObjectManager接口
        else if (interface_str == "org.freedesktop.DBus.ObjectManager") {
            if (member_str == "GetManagedObjects") {
                handle_get_managed_objects(message);
                return;
            }
        }
        // 处理Introspectable接口
        else if (interface_str == "org.freedesktop.DBus.Introspectable") {
            if (member_str == "Introspect") {
                handle_introspect(message);
                return;
            }
        }
        // 处理LEAdvertisement1接口
        else if (interface_str == "org.bluez.LEAdvertisement1") {
            log_handler_->debug("LEAdvertisement1 method call: {}", member_str);
            // LEAdvertisement1接口通常只需要属性访问，通过Properties接口处理
            // 这里不需要特殊处理，但记录日志以便调试
        }

        // 发送错误回复给未处理的方法
        reply = dbus_message_new_error(message, "org.freedesktop.DBus.Error.UnknownMethod",
                                     "Method not implemented");
        if (reply) {
            dbus_connection_send(dbus_connection_, reply, nullptr);
            dbus_message_unref(reply);
        }

    } catch (const std::exception& e) {
        log_handler_->error("Exception in GATT message handler: {}", e.what());
        reply = dbus_message_new_error(message, "org.freedesktop.DBus.Error.Failed", e.what());
        if (reply) {
            dbus_connection_send(dbus_connection_, reply, nullptr);
            dbus_message_unref(reply);
        }
    }
}

bool BluetoothManager::process_wifi_data(const std::string &data) {
    try {
        json json_data = json::parse(data);
        
        WifiInfo new_wifi_info;
        new_wifi_info.ssid = json_data.value("ssid", "");
        new_wifi_info.password = json_data.value("password", "");
        new_wifi_info.is_valid = validate_wifi_info(new_wifi_info);
        
        if (new_wifi_info.is_valid) {
            // 使用ConfigManager写入WiFi配置
            auto& config_manager = ConfigManager::getInstance();
            if (config_manager.setWifiConfig(new_wifi_info.ssid, new_wifi_info.password)) {
                std::lock_guard<std::mutex> lock(wifi_info_mutex_);
                wifi_info_ = new_wifi_info;
                log_handler_->info("WiFi configuration saved successfully: SSID={}", new_wifi_info.ssid);
                return true;
            } else {
                log_handler_->error("Failed to write WiFi configuration");
                return false;
            }
        }
        
        return false;
    } catch (const std::exception &e) {
        log_handler_->error("Exception processing WiFi data: {}", e.what());
        return false;
    }
}

bool BluetoothManager::process_device_reg_data(const std::string &data) {
    try {
        json json_data = json::parse(data);
        
        DeviceRegInfo new_reg_info;
        new_reg_info.user_id = json_data.value("user_id", "");
        new_reg_info.is_valid = validate_device_reg_info(new_reg_info);
        
        if (new_reg_info.is_valid) {
            // 使用ConfigManager写入设备配置
            auto& config_manager = ConfigManager::getInstance();
            if (config_manager.setUserId(new_reg_info.user_id)) {
                std::lock_guard<std::mutex> lock(device_reg_info_mutex_);
                device_reg_info_ = new_reg_info;
                log_handler_->info("Device registration saved successfully: User ID={}", 
                                 new_reg_info.user_id);
                return true;
            } else {
                log_handler_->error("Failed to write device configuration");
                return false;
            }
        }
        
        return false;
    } catch (const std::exception &e) {
        log_handler_->error("Exception processing device registration data: {}", e.what());
        return false;
    }
}

bool BluetoothManager::validate_wifi_info(const WifiInfo &info) {
    if (info.ssid.empty()) {
        log_handler_->error("WiFi SSID cannot be empty");
        return false;
    }
    
    if (info.password.empty()) {
        log_handler_->error("WiFi password cannot be empty");
        return false;
    }
    
    if (info.ssid.length() > 32) {
        log_handler_->error("WiFi SSID too long (max 32 characters)");
        return false;
    }
    
    if (info.password.length() < 8) {
        log_handler_->error("WiFi password too short (min 8 characters)");
        return false;
    }
    
    return true;
}

bool BluetoothManager::validate_device_reg_info(const DeviceRegInfo &info) {
    if (info.user_id.empty()) {
        log_handler_->error("User ID cannot be empty");
        return false;
    }

    if (info.user_id.length() < 8 || info.user_id.length() > 50) {
      log_handler_->error("User ID  format error");
      return false;
    }

    return true;
}

// 嵌入式蓝牙命令实现
bool BluetoothManager::execute_disable_classic_bt() {
    log_handler_->info("Executing disable classic bluetooth commands");
    
    std::vector<std::string> commands = {
        "hciconfig hci0 down",
        "sleep 1",
        "hciconfig hci0 up",
        "echo 'class 0x000000' | bluetoothctl",
        "sleep 2"
    };
    
    for (const auto& cmd : commands) {
        std::string result = execute_command(cmd);
        if (result.find("ERROR") != std::string::npos) {
            log_handler_->warn("Command may have failed: {}", cmd);
        }
    }
    
    return true;
}

bool BluetoothManager::execute_enable_ble() {
    log_handler_->info("Executing enable BLE commands");
    
    // 获取硬件序列号并生成动态系统别名
    auto& config_manager = aby_box::ConfigManager::getInstance();
    std::string hardware_sn = config_manager.getHardwareSn();
    std::string system_alias = "AbyBox" + hardware_sn;
    
    log_handler_->info("Setting Bluetooth system alias to: {}", system_alias);
    
    std::vector<std::string> commands = {
        "hciconfig hci0 up",
        "hciconfig hci0 leadv 3",
        "hciconfig hci0 lescan",
        "echo 'power on' | bluetoothctl",
        "echo 'agent NoInputNoOutput' | bluetoothctl",
        "echo 'default-agent' | bluetoothctl",
        "echo 'system-alias " + system_alias + "' | bluetoothctl"
    };
    
    for (const auto& cmd : commands) {
        std::string result = execute_command(cmd);
        if (result.find("ERROR") != std::string::npos) {
            log_handler_->warn("Command may have failed: {}", cmd);
        }
    }
    
    return true;
}

bool BluetoothManager::execute_start_gatt_server() {
    log_handler_->info("Executing start GATT server commands");
    
    std::vector<std::string> commands = {
        "echo 'power on' | bluetoothctl",
        "echo 'agent NoInputNoOutput' | bluetoothctl",
        "echo 'default-agent' | bluetoothctl",
        "echo 'advertise on' | bluetoothctl"
    };
    
    for (const auto& cmd : commands) {
        std::string result = execute_command(cmd);
        if (result.find("ERROR") != std::string::npos) {
            log_handler_->warn("Command may have failed: {}", cmd);
        }
    }
    
    return true;
}

bool BluetoothManager::execute_stop_gatt_server() {
    log_handler_->info("Executing stop GATT server commands");
    
    std::vector<std::string> commands = {
        "echo 'advertise off' | bluetoothctl",
        "echo 'discoverable off' | bluetoothctl",
        "echo 'pairable off' | bluetoothctl"
    };
    
    for (const auto& cmd : commands) {
        execute_command(cmd);
    }
    
    return true;
}

std::string BluetoothManager::execute_command(const std::string &command) {
    std::array<char, 128> buffer;
    std::string result;
    
    std::unique_ptr<FILE, decltype(&pclose)> pipe(popen(command.c_str(), "r"), pclose);
    if (!pipe) {
        log_handler_->error("Failed to execute command: {}", command);
        return "";
    }
    
    while (fgets(buffer.data(), buffer.size(), pipe.get()) != nullptr) {
        result += buffer.data();
    }
    
    return result;
}

bool BluetoothManager::call_dbus_method(const std::string& service, const std::string& path,
                                       const std::string& interface, const std::string& method,
                                       DBusMessage** reply) {
    if (!dbus_connection_) {
        return false;
    }
    
    std::lock_guard<std::mutex> lock(dbus_mutex_);
    
    DBusMessage* message = dbus_message_new_method_call(service.c_str(), path.c_str(),
                                                       interface.c_str(), method.c_str());
    if (!message) {
        return false;
    }
    
    DBusMessage* local_reply = nullptr;
    DBusError error;
    dbus_error_init(&error);
    
    local_reply = dbus_connection_send_with_reply_and_block(dbus_connection_, message, 
                                                           DBUS_TIMEOUT_USE_DEFAULT, &error);
    
    dbus_message_unref(message);
    
    if (dbus_error_is_set(&error)) {
        log_handler_->error("DBus method call failed: {}", error.message);
        dbus_error_free(&error);
        return false;
    }
    
    if (reply) {
        *reply = local_reply;
    } else if (local_reply) {
        dbus_message_unref(local_reply);
    }
    
    return true;
}

bool BluetoothManager::register_dbus_object_path(const std::string& path) {
    if (!dbus_connection_) {
        return false;
    }
    
    std::lock_guard<std::mutex> lock(dbus_mutex_);
    
    // 注册对象路径到DBus - 这会使路径可以接收消息
    if (!dbus_connection_register_object_path(dbus_connection_, path.c_str(), 
                                             &dbus_vtable_, this)) {
        log_handler_->error("Failed to register DBus object path: {}", path);
        return false;
    }
    
    log_handler_->info("Registered DBus object path: {}", path);
    return true;
}

std::string BluetoothManager::get_introspect_xml(const std::string& path) {
    std::ostringstream xml;
    
    xml << "<?xml version=\"1.0\" encoding=\"UTF-8\" ?>\n";
    xml << "<node>\n";
    xml << "  <interface name=\"org.freedesktop.DBus.Introspectable\">\n";
    xml << "    <method name=\"Introspect\">\n";
    xml << "      <arg name=\"xml\" direction=\"out\" type=\"s\"/>\n";
    xml << "    </method>\n";
    xml << "  </interface>\n";
    
    if (path == "/com/animsi/abybox") {
        // 应用程序根节点
        xml << "  <interface name=\"org.freedesktop.DBus.ObjectManager\">\n";
        xml << "    <method name=\"GetManagedObjects\">\n";
        xml << "      <arg name=\"objects\" direction=\"out\" type=\"a{oa{sa{sv}}}\"/>\n";
        xml << "    </method>\n";
        xml << "  </interface>\n";
        xml << "  <node name=\"service\"/>\n";
        xml << "  <node name=\"advertisement\"/>\n";
    } else if (path == GATT_SERVICE_PATH) {
        // GATT服务节点
        xml << "  <interface name=\"org.bluez.GattService1\">\n";
        xml << "    <property name=\"UUID\" type=\"s\" access=\"read\"/>\n";
        xml << "    <property name=\"Primary\" type=\"b\" access=\"read\"/>\n";
        xml << "  </interface>\n";
        xml << "  <node name=\"wifi_char\"/>\n";
        xml << "  <node name=\"device_reg_char\"/>\n";
    } else if (path == WIFI_CHAR_PATH) {
        // WiFi特征值节点
        xml << "  <interface name=\"org.bluez.GattCharacteristic1\">\n";
        xml << "    <method name=\"ReadValue\">\n";
        xml << "      <arg name=\"options\" direction=\"in\" type=\"a{sv}\"/>\n";
        xml << "      <arg name=\"value\" direction=\"out\" type=\"ay\"/>\n";
        xml << "    </method>\n";
        xml << "    <method name=\"WriteValue\">\n";
        xml << "      <arg name=\"value\" direction=\"in\" type=\"ay\"/>\n";
        xml << "      <arg name=\"options\" direction=\"in\" type=\"a{sv}\"/>\n";
        xml << "    </method>\n";
        xml << "    <property name=\"UUID\" type=\"s\" access=\"read\"/>\n";
        xml << "    <property name=\"Service\" type=\"o\" access=\"read\"/>\n";
        xml << "    <property name=\"Flags\" type=\"as\" access=\"read\"/>\n";
        xml << "  </interface>\n";
    } else if (path == DEVICE_REG_CHAR_PATH) {
        // 设备注册特征值节点
        xml << "  <interface name=\"org.bluez.GattCharacteristic1\">\n";
        xml << "    <method name=\"ReadValue\">\n";
        xml << "      <arg name=\"options\" direction=\"in\" type=\"a{sv}\"/>\n";
        xml << "      <arg name=\"value\" direction=\"out\" type=\"ay\"/>\n";
        xml << "    </method>\n";
        xml << "    <method name=\"WriteValue\">\n";
        xml << "      <arg name=\"value\" direction=\"in\" type=\"ay\"/>\n";
        xml << "      <arg name=\"options\" direction=\"in\" type=\"a{sv}\"/>\n";
        xml << "    </method>\n";
        xml << "    <property name=\"UUID\" type=\"s\" access=\"read\"/>\n";
        xml << "    <property name=\"Service\" type=\"o\" access=\"read\"/>\n";
        xml << "    <property name=\"Flags\" type=\"as\" access=\"read\"/>\n";
        xml << "  </interface>\n";
    } else if (path == ADVERTISEMENT_PATH) {
        // 广告对象节点
        xml << "  <interface name=\"org.bluez.LEAdvertisement1\">\n";
        xml << "    <property name=\"Type\" type=\"s\" access=\"read\"/>\n";
        xml << "    <property name=\"ServiceUUIDs\" type=\"as\" access=\"read\"/>\n";
        xml << "    <property name=\"LocalName\" type=\"s\" access=\"read\"/>\n";
        xml << "  </interface>\n";
    }

    xml << "</node>\n";
    return xml.str();
}





void BluetoothManager::add_characteristic_to_managed_objects(DBusMessageIter* dict_iter,
                                                           const char* char_path,
                                                           const char* char_uuid,
                                                           const char* service_path) {
    DBusMessageIter entry_iter, iface_dict_iter, char_iface_iter, char_props_iter;
    
    // 特征值对象条目
    dbus_message_iter_open_container(dict_iter, DBUS_TYPE_DICT_ENTRY, NULL, &entry_iter);
    dbus_message_iter_append_basic(&entry_iter, DBUS_TYPE_OBJECT_PATH, &char_path);
    
    // 接口字典
    dbus_message_iter_open_container(&entry_iter, DBUS_TYPE_ARRAY, "{sa{sv}}", &iface_dict_iter);
    
    // GattCharacteristic1接口
    dbus_message_iter_open_container(&iface_dict_iter, DBUS_TYPE_DICT_ENTRY, NULL, &char_iface_iter);
    const char* char_interface = "org.bluez.GattCharacteristic1";
    dbus_message_iter_append_basic(&char_iface_iter, DBUS_TYPE_STRING, &char_interface);
    
    dbus_message_iter_open_container(&char_iface_iter, DBUS_TYPE_ARRAY, "{sv}", &char_props_iter);
    
    // UUID属性
    DBusMessageIter uuid_entry_iter, uuid_variant_iter;
    dbus_message_iter_open_container(&char_props_iter, DBUS_TYPE_DICT_ENTRY, NULL, &uuid_entry_iter);
    const char* uuid_prop = "UUID";
    dbus_message_iter_append_basic(&uuid_entry_iter, DBUS_TYPE_STRING, &uuid_prop);
    dbus_message_iter_open_container(&uuid_entry_iter, DBUS_TYPE_VARIANT, "s", &uuid_variant_iter);
    dbus_message_iter_append_basic(&uuid_variant_iter, DBUS_TYPE_STRING, &char_uuid);
    dbus_message_iter_close_container(&uuid_entry_iter, &uuid_variant_iter);
    dbus_message_iter_close_container(&char_props_iter, &uuid_entry_iter);
    
    // Service属性
    DBusMessageIter service_entry_iter, service_variant_iter;
    dbus_message_iter_open_container(&char_props_iter, DBUS_TYPE_DICT_ENTRY, NULL, &service_entry_iter);
    const char* service_prop = "Service";
    dbus_message_iter_append_basic(&service_entry_iter, DBUS_TYPE_STRING, &service_prop);
    dbus_message_iter_open_container(&service_entry_iter, DBUS_TYPE_VARIANT, "o", &service_variant_iter);
    dbus_message_iter_append_basic(&service_variant_iter, DBUS_TYPE_OBJECT_PATH, &service_path);
    dbus_message_iter_close_container(&service_entry_iter, &service_variant_iter);
    dbus_message_iter_close_container(&char_props_iter, &service_entry_iter);
    
    // Flags属性
    DBusMessageIter flags_entry_iter, flags_variant_iter, flags_array_iter;
    dbus_message_iter_open_container(&char_props_iter, DBUS_TYPE_DICT_ENTRY, NULL, &flags_entry_iter);
    const char* flags_prop = "Flags";
    dbus_message_iter_append_basic(&flags_entry_iter, DBUS_TYPE_STRING, &flags_prop);
    dbus_message_iter_open_container(&flags_entry_iter, DBUS_TYPE_VARIANT, "as", &flags_variant_iter);
    dbus_message_iter_open_container(&flags_variant_iter, DBUS_TYPE_ARRAY, "s", &flags_array_iter);
    const char* read_flag = "read";
    const char* write_flag = "write";
    dbus_message_iter_append_basic(&flags_array_iter, DBUS_TYPE_STRING, &read_flag);
    dbus_message_iter_append_basic(&flags_array_iter, DBUS_TYPE_STRING, &write_flag);
    dbus_message_iter_close_container(&flags_variant_iter, &flags_array_iter);
    dbus_message_iter_close_container(&flags_entry_iter, &flags_variant_iter);
    dbus_message_iter_close_container(&char_props_iter, &flags_entry_iter);
    
    dbus_message_iter_close_container(&char_iface_iter, &char_props_iter);
    dbus_message_iter_close_container(&iface_dict_iter, &char_iface_iter);
    dbus_message_iter_close_container(&entry_iter, &iface_dict_iter);
    dbus_message_iter_close_container(dict_iter, &entry_iter);
}

void BluetoothManager::add_service_object_to_managed_objects(DBusMessageIter* objects_array) {
    DBusMessageIter service_entry, service_interfaces, service_iface_entry, service_props;
    // 服务对象条目
    dbus_message_iter_open_container(objects_array, DBUS_TYPE_DICT_ENTRY, NULL, &service_entry);
    const char* service_path = GATT_SERVICE_PATH;
    dbus_message_iter_append_basic(&service_entry, DBUS_TYPE_OBJECT_PATH, &service_path);
    
    // 接口字典
    dbus_message_iter_open_container(&service_entry, DBUS_TYPE_ARRAY, "{sa{sv}}", &service_interfaces);
    
    // GattService1接口
    dbus_message_iter_open_container(&service_interfaces, DBUS_TYPE_DICT_ENTRY, NULL, &service_iface_entry);
    const char* service_interface = "org.bluez.GattService1";
    dbus_message_iter_append_basic(&service_iface_entry, DBUS_TYPE_STRING, &service_interface);
    
    // 服务属性
    dbus_message_iter_open_container(&service_iface_entry, DBUS_TYPE_ARRAY, "{sv}", &service_props);
    
    // UUID属性
    DBusMessageIter uuid_entry, uuid_variant;
    dbus_message_iter_open_container(&service_props, DBUS_TYPE_DICT_ENTRY, NULL, &uuid_entry);
    const char* uuid_prop = "UUID";
    dbus_message_iter_append_basic(&uuid_entry, DBUS_TYPE_STRING, &uuid_prop);
    dbus_message_iter_open_container(&uuid_entry, DBUS_TYPE_VARIANT, "s", &uuid_variant);
    const char* service_uuid = DEVICE_SERVICE_UUID;
    dbus_message_iter_append_basic(&uuid_variant, DBUS_TYPE_STRING, &service_uuid);
    dbus_message_iter_close_container(&uuid_entry, &uuid_variant);
    dbus_message_iter_close_container(&service_props, &uuid_entry);
    
    // Primary属性
    DBusMessageIter primary_entry, primary_variant;
    dbus_message_iter_open_container(&service_props, DBUS_TYPE_DICT_ENTRY, NULL, &primary_entry);
    const char* primary_prop = "Primary";
    dbus_message_iter_append_basic(&primary_entry, DBUS_TYPE_STRING, &primary_prop);
    dbus_message_iter_open_container(&primary_entry, DBUS_TYPE_VARIANT, "b", &primary_variant);
    dbus_bool_t is_primary = TRUE;
    dbus_message_iter_append_basic(&primary_variant, DBUS_TYPE_BOOLEAN, &is_primary);
    dbus_message_iter_close_container(&primary_entry, &primary_variant);
    dbus_message_iter_close_container(&service_props, &primary_entry);
    
    dbus_message_iter_close_container(&service_iface_entry, &service_props);
    dbus_message_iter_close_container(&service_interfaces, &service_iface_entry);
    dbus_message_iter_close_container(&service_entry, &service_interfaces);
    dbus_message_iter_close_container(objects_array, &service_entry);
}

bool BluetoothManager::check_bluez_adapter_ready() {
    if (!dbus_connection_) {
        log_handler_->error("DBus connection not available for adapter check");
        return false;
    }
    
    log_handler_->info("Checking BlueZ adapter readiness for path: {}", config_.adapter_path);
    
    // 简化检查：直接检查GATT管理器接口是否可用
    DBusMessage* reply = nullptr;
    if (!call_dbus_method(BLUEZ_SERVICE, config_.adapter_path,
                         "org.freedesktop.DBus.Introspectable", "Introspect", &reply)) {
        log_handler_->warn("Failed to introspect adapter, proceeding anyway");
        return true; // 即使内省失败也继续尝试
    }
    
    if (reply) {
        // 检查内省结果是否包含GattManager接口
        DBusMessageIter iter;
        if (dbus_message_iter_init(reply, &iter)) {
            const char* introspect_xml = nullptr;
            if (dbus_message_iter_get_arg_type(&iter) == DBUS_TYPE_STRING) {
                dbus_message_iter_get_basic(&iter, &introspect_xml);
                if (introspect_xml) {
                    if (strstr(introspect_xml, "GattManager1")) {
                        log_handler_->info("BlueZ adapter has GattManager1 interface");
                        dbus_message_unref(reply);
                        return true;
                    }
                }
            }
        }
        dbus_message_unref(reply);
    }
    
    // 即使检查失败也返回true，因为有些BlueZ版本可能不支持完整的内省
    return true;
}

// GATT接口处理方法
void BluetoothManager::handle_properties_get_all(DBusMessage* message) {
    const char* path = dbus_message_get_path(message);

    // 获取接口名称参数
    DBusMessageIter args;
    const char* interface_name = nullptr;
    if (dbus_message_iter_init(message, &args) &&
        dbus_message_iter_get_arg_type(&args) == DBUS_TYPE_STRING) {
        dbus_message_iter_get_basic(&args, &interface_name);
    }

    log_handler_->debug("Properties GetAll request: path={}, interface={}",
                       path ? path : "null", interface_name ? interface_name : "null");

    DBusMessage* reply = dbus_message_new_method_return(message);
    if (!reply) {
        return;
    }

    DBusMessageIter iter, dict_iter, entry_iter, variant_iter;
    dbus_message_iter_init_append(reply, &iter);
    dbus_message_iter_open_container(&iter, DBUS_TYPE_ARRAY, "{sv}", &dict_iter);

    if (std::string(path) == GATT_SERVICE_PATH &&
        interface_name && std::string(interface_name) == "org.bluez.GattService1") {
        // 服务属性
        dbus_message_iter_open_container(&dict_iter, DBUS_TYPE_DICT_ENTRY, nullptr, &entry_iter);
        const char* uuid_key = "UUID";
        dbus_message_iter_append_basic(&entry_iter, DBUS_TYPE_STRING, &uuid_key);
        dbus_message_iter_open_container(&entry_iter, DBUS_TYPE_VARIANT, "s", &variant_iter);
        const char* service_uuid = DEVICE_SERVICE_UUID;
        dbus_message_iter_append_basic(&variant_iter, DBUS_TYPE_STRING, &service_uuid);
        dbus_message_iter_close_container(&entry_iter, &variant_iter);
        dbus_message_iter_close_container(&dict_iter, &entry_iter);

        dbus_message_iter_open_container(&dict_iter, DBUS_TYPE_DICT_ENTRY, nullptr, &entry_iter);
        const char* primary_key = "Primary";
        dbus_message_iter_append_basic(&entry_iter, DBUS_TYPE_STRING, &primary_key);
        dbus_message_iter_open_container(&entry_iter, DBUS_TYPE_VARIANT, "b", &variant_iter);
        dbus_bool_t primary = TRUE;
        dbus_message_iter_append_basic(&variant_iter, DBUS_TYPE_BOOLEAN, &primary);
        dbus_message_iter_close_container(&entry_iter, &variant_iter);
        dbus_message_iter_close_container(&dict_iter, &entry_iter);
    } else if (std::string(path) == WIFI_CHAR_PATH &&
               interface_name && std::string(interface_name) == "org.bluez.GattCharacteristic1") {
        // WiFi特征属性
        dbus_message_iter_open_container(&dict_iter, DBUS_TYPE_DICT_ENTRY, nullptr, &entry_iter);
        const char* uuid_key = "UUID";
        dbus_message_iter_append_basic(&entry_iter, DBUS_TYPE_STRING, &uuid_key);
        dbus_message_iter_open_container(&entry_iter, DBUS_TYPE_VARIANT, "s", &variant_iter);
        const char* char_uuid = WIFI_CHARACTERISTIC_UUID;
        dbus_message_iter_append_basic(&variant_iter, DBUS_TYPE_STRING, &char_uuid);
        dbus_message_iter_close_container(&entry_iter, &variant_iter);
        dbus_message_iter_close_container(&dict_iter, &entry_iter);

        dbus_message_iter_open_container(&dict_iter, DBUS_TYPE_DICT_ENTRY, nullptr, &entry_iter);
        const char* service_key = "Service";
        dbus_message_iter_append_basic(&entry_iter, DBUS_TYPE_STRING, &service_key);
        dbus_message_iter_open_container(&entry_iter, DBUS_TYPE_VARIANT, "o", &variant_iter);
        const char* service_path = GATT_SERVICE_PATH;
        dbus_message_iter_append_basic(&variant_iter, DBUS_TYPE_OBJECT_PATH, &service_path);
        dbus_message_iter_close_container(&entry_iter, &variant_iter);
        dbus_message_iter_close_container(&dict_iter, &entry_iter);

        // Flags
        dbus_message_iter_open_container(&dict_iter, DBUS_TYPE_DICT_ENTRY, nullptr, &entry_iter);
        const char* flags_key = "Flags";
        dbus_message_iter_append_basic(&entry_iter, DBUS_TYPE_STRING, &flags_key);
        dbus_message_iter_open_container(&entry_iter, DBUS_TYPE_VARIANT, "as", &variant_iter);
        DBusMessageIter flags_iter;
        dbus_message_iter_open_container(&variant_iter, DBUS_TYPE_ARRAY, "s", &flags_iter);
        const char* read_flag = "read";
        const char* write_flag = "write";
        const char* notify_flag = "notify";
        dbus_message_iter_append_basic(&flags_iter, DBUS_TYPE_STRING, &read_flag);
        dbus_message_iter_append_basic(&flags_iter, DBUS_TYPE_STRING, &write_flag);
        dbus_message_iter_append_basic(&flags_iter, DBUS_TYPE_STRING, &notify_flag);
        dbus_message_iter_close_container(&variant_iter, &flags_iter);
        dbus_message_iter_close_container(&entry_iter, &variant_iter);
        dbus_message_iter_close_container(&dict_iter, &entry_iter);
    } else if (std::string(path) == DEVICE_REG_CHAR_PATH &&
               interface_name && std::string(interface_name) == "org.bluez.GattCharacteristic1") {
        // 设备注册特征属性
        dbus_message_iter_open_container(&dict_iter, DBUS_TYPE_DICT_ENTRY, nullptr, &entry_iter);
        const char* uuid_key = "UUID";
        dbus_message_iter_append_basic(&entry_iter, DBUS_TYPE_STRING, &uuid_key);
        dbus_message_iter_open_container(&entry_iter, DBUS_TYPE_VARIANT, "s", &variant_iter);
        const char* char_uuid = DEVICE_REG_CHARACTERISTIC_UUID;
        dbus_message_iter_append_basic(&variant_iter, DBUS_TYPE_STRING, &char_uuid);
        dbus_message_iter_close_container(&entry_iter, &variant_iter);
        dbus_message_iter_close_container(&dict_iter, &entry_iter);

        dbus_message_iter_open_container(&dict_iter, DBUS_TYPE_DICT_ENTRY, nullptr, &entry_iter);
        const char* service_key = "Service";
        dbus_message_iter_append_basic(&entry_iter, DBUS_TYPE_STRING, &service_key);
        dbus_message_iter_open_container(&entry_iter, DBUS_TYPE_VARIANT, "o", &variant_iter);
        const char* service_path = GATT_SERVICE_PATH;
        dbus_message_iter_append_basic(&variant_iter, DBUS_TYPE_OBJECT_PATH, &service_path);
        dbus_message_iter_close_container(&entry_iter, &variant_iter);
        dbus_message_iter_close_container(&dict_iter, &entry_iter);

        // Flags
        dbus_message_iter_open_container(&dict_iter, DBUS_TYPE_DICT_ENTRY, nullptr, &entry_iter);
        const char* flags_key = "Flags";
        dbus_message_iter_append_basic(&entry_iter, DBUS_TYPE_STRING, &flags_key);
        dbus_message_iter_open_container(&entry_iter, DBUS_TYPE_VARIANT, "as", &variant_iter);
        DBusMessageIter flags_iter;
        dbus_message_iter_open_container(&variant_iter, DBUS_TYPE_ARRAY, "s", &flags_iter);
        const char* read_flag = "read";
        const char* write_flag = "write";
        const char* notify_flag = "notify";
        dbus_message_iter_append_basic(&flags_iter, DBUS_TYPE_STRING, &read_flag);
        dbus_message_iter_append_basic(&flags_iter, DBUS_TYPE_STRING, &write_flag);
        dbus_message_iter_append_basic(&flags_iter, DBUS_TYPE_STRING, &notify_flag);
        dbus_message_iter_close_container(&variant_iter, &flags_iter);
        dbus_message_iter_close_container(&entry_iter, &variant_iter);
        dbus_message_iter_close_container(&dict_iter, &entry_iter);
    } else if (std::string(path) == ADVERTISEMENT_PATH &&
               interface_name && std::string(interface_name) == "org.bluez.LEAdvertisement1") {
        // 广告对象属性
        dbus_message_iter_open_container(&dict_iter, DBUS_TYPE_DICT_ENTRY, nullptr, &entry_iter);
        const char* type_key = "Type";
        dbus_message_iter_append_basic(&entry_iter, DBUS_TYPE_STRING, &type_key);
        dbus_message_iter_open_container(&entry_iter, DBUS_TYPE_VARIANT, "s", &variant_iter);
        const char* type_value = "peripheral";
        dbus_message_iter_append_basic(&variant_iter, DBUS_TYPE_STRING, &type_value);
        dbus_message_iter_close_container(&entry_iter, &variant_iter);
        dbus_message_iter_close_container(&dict_iter, &entry_iter);

        // ServiceUUIDs属性
        dbus_message_iter_open_container(&dict_iter, DBUS_TYPE_DICT_ENTRY, nullptr, &entry_iter);
        const char* service_uuids_key = "ServiceUUIDs";
        dbus_message_iter_append_basic(&entry_iter, DBUS_TYPE_STRING, &service_uuids_key);
        dbus_message_iter_open_container(&entry_iter, DBUS_TYPE_VARIANT, "as", &variant_iter);
        DBusMessageIter service_uuids_iter;
        dbus_message_iter_open_container(&variant_iter, DBUS_TYPE_ARRAY, "s", &service_uuids_iter);
        const char* service_uuid = DEVICE_SERVICE_UUID;
        dbus_message_iter_append_basic(&service_uuids_iter, DBUS_TYPE_STRING, &service_uuid);
        dbus_message_iter_close_container(&variant_iter, &service_uuids_iter);
        dbus_message_iter_close_container(&entry_iter, &variant_iter);
        dbus_message_iter_close_container(&dict_iter, &entry_iter);

        // LocalName属性
        dbus_message_iter_open_container(&dict_iter, DBUS_TYPE_DICT_ENTRY, nullptr, &entry_iter);
        const char* local_name_key = "LocalName";
        dbus_message_iter_append_basic(&entry_iter, DBUS_TYPE_STRING, &local_name_key);
        dbus_message_iter_open_container(&entry_iter, DBUS_TYPE_VARIANT, "s", &variant_iter);
        auto& config_manager = ConfigManager::getInstance();
        static std::string device_name = "AbyBox" + config_manager.getHardwareSn();
        const char* name_value = device_name.c_str();
        dbus_message_iter_append_basic(&variant_iter, DBUS_TYPE_STRING, &name_value);
        dbus_message_iter_close_container(&entry_iter, &variant_iter);
        dbus_message_iter_close_container(&dict_iter, &entry_iter);
    }

    dbus_message_iter_close_container(&iter, &dict_iter);

    // 发送回复
    dbus_connection_send(dbus_connection_, reply, nullptr);
    dbus_message_unref(reply);
}

void BluetoothManager::handle_properties_get(DBusMessage* message) {
    // 简化实现，可以根据需要扩展
}

void BluetoothManager::handle_characteristic_read_value(DBusMessage* message) {

    DBusMessage* reply = dbus_message_new_method_return(message);
    if (!reply) {
        return;
    }

    const char* path = dbus_message_get_path(message);
    std::string response_data;

    if (std::string(path) == WIFI_CHAR_PATH) {
        // WiFi特征值读取 - 从ConfigManager获取
        auto& config_manager = ConfigManager::getInstance();
        json json_data;
        json_data["ssid"] = config_manager.getWifiSSID();
        json_data["password"] = config_manager.getWifiPassword();
        json_data["is_valid"] = config_manager.isWifiConfigValid();
        response_data = json_data.dump();
    } else if (std::string(path) == DEVICE_REG_CHAR_PATH) {
        // 设备注册特征值读取 - 从ConfigManager获取
        auto& config_manager = ConfigManager::getInstance();
        json json_data;
        json_data["hardware_sn"] = config_manager.getHardwareSn();
        json_data["user_id"] = config_manager.getUserId();
        json_data["device_id"] = config_manager.getDeviceId();
        json_data["version_id"] = config_manager.getVersionId();
        json_data["is_valid"] = !config_manager.getUserId().empty() && 
                               !config_manager.getDeviceId().empty() &&
                               !config_manager.getHardwareSn().empty();
        response_data = json_data.dump();
    }

    DBusMessageIter iter, array_iter;
    dbus_message_iter_init_append(reply, &iter);
    dbus_message_iter_open_container(&iter, DBUS_TYPE_ARRAY, "y", &array_iter);

    for (char c : response_data) {
        uint8_t byte = static_cast<uint8_t>(c);
        dbus_message_iter_append_basic(&array_iter, DBUS_TYPE_BYTE, &byte);
    }

    dbus_message_iter_close_container(&iter, &array_iter);

    dbus_connection_send(dbus_connection_, reply, nullptr);
    dbus_message_unref(reply);

    log_handler_->debug("Characteristic read request handled, {} bytes sent", response_data.size());
}

void BluetoothManager::handle_characteristic_write_value(DBusMessage* message) {

    DBusMessageIter iter, array_iter;
    if (!dbus_message_iter_init(message, &iter)) {
        return;
    }

    std::vector<uint8_t> new_value;
    const char* path = dbus_message_get_path(message);

    // 第一个参数应该是字节数组（值）
    if (dbus_message_iter_get_arg_type(&iter) == DBUS_TYPE_ARRAY) {
        dbus_message_iter_recurse(&iter, &array_iter);

        while (dbus_message_iter_get_arg_type(&array_iter) == DBUS_TYPE_BYTE) {
            uint8_t byte;
            dbus_message_iter_get_basic(&array_iter, &byte);
            new_value.push_back(byte);
            dbus_message_iter_next(&array_iter);
        }
    }

    if (!new_value.empty()) {
        std::string data(new_value.begin(), new_value.end());
        bool success = false;

        if (std::string(path) == WIFI_CHAR_PATH) {
            success = process_wifi_data(data);
        } else if (std::string(path) == DEVICE_REG_CHAR_PATH) {
            success = process_device_reg_data(data);
        }

        log_handler_->debug("Characteristic write: {} bytes received - success: {}", new_value.size(), success);
    }

    // 发送确认回复
    DBusMessage* reply = dbus_message_new_method_return(message);
    if (reply) {
        dbus_connection_send(dbus_connection_, reply, nullptr);
        dbus_message_unref(reply);
    }
}

void BluetoothManager::handle_introspect(DBusMessage* message) {

    const char* path = dbus_message_get_path(message);
    std::string introspect_xml = get_introspect_xml(path);

    DBusMessage* reply = dbus_message_new_method_return(message);
    if (!reply) {
        return;
    }

    const char* xml_data = introspect_xml.c_str();
    dbus_message_append_args(reply, DBUS_TYPE_STRING, &xml_data, DBUS_TYPE_INVALID);

    dbus_connection_send(dbus_connection_, reply, nullptr);
    dbus_message_unref(reply);
}

void BluetoothManager::handle_get_managed_objects(DBusMessage* message) {

    DBusMessage* reply = dbus_message_new_method_return(message);
    if (!reply) {
        return;
    }

    DBusMessageIter iter, dict_iter;
    dbus_message_iter_init_append(reply, &iter);
    dbus_message_iter_open_container(&iter, DBUS_TYPE_ARRAY, "{oa{sa{sv}}}", &dict_iter);

    // 添加服务对象
    add_service_object_to_managed_objects(&dict_iter);

    // 添加WiFi特征值对象
    add_characteristic_to_managed_objects(&dict_iter, WIFI_CHAR_PATH, WIFI_CHARACTERISTIC_UUID, GATT_SERVICE_PATH);

    // 添加设备注册特征值对象
    add_characteristic_to_managed_objects(&dict_iter, DEVICE_REG_CHAR_PATH, DEVICE_REG_CHARACTERISTIC_UUID, GATT_SERVICE_PATH);

    dbus_message_iter_close_container(&iter, &dict_iter);

    // 发送回复
    dbus_connection_send(dbus_connection_, reply, nullptr);
    dbus_message_unref(reply);
}

// 广告管理器相关方法实现
bool BluetoothManager::setup_advertisement_object() {
    log_handler_->info("Setting up advertisement object");
    return true;
}

bool BluetoothManager::register_advertisement() {
    if (advertisement_registered_) {
        log_handler_->warn("Advertisement already registered");
        return true;
    }

    log_handler_->info("Registering BLE advertisement with service UUID");

    if (!dbus_connection_) {
        log_handler_->error("DBus connection not available for advertisement");
        return false;
    }

    std::lock_guard<std::mutex> lock(dbus_mutex_);

    // 创建RegisterAdvertisement方法调用
    DBusMessage* message = dbus_message_new_method_call(
        BLUEZ_SERVICE,
        config_.adapter_path.c_str(),
        BLUEZ_ADVERTISING_MANAGER_INTERFACE,
        "RegisterAdvertisement"
    );

    if (!message) {
        log_handler_->error("Failed to create RegisterAdvertisement message");
        return false;
    }

    // 设置参数
    DBusMessageIter iter, dict_iter;
    dbus_message_iter_init_append(message, &iter);

    // 第一个参数：广告对象路径
    const char* ad_path = ADVERTISEMENT_PATH;
    dbus_message_iter_append_basic(&iter, DBUS_TYPE_OBJECT_PATH, &ad_path);

    // 第二个参数：选项字典（空）
    dbus_message_iter_open_container(&iter, DBUS_TYPE_ARRAY, "{sv}", &dict_iter);
    dbus_message_iter_close_container(&iter, &dict_iter);

    // 发送消息并等待回复
    DBusError error;
    dbus_error_init(&error);
    DBusMessage* reply = dbus_connection_send_with_reply_and_block(
        dbus_connection_, message, 5000, &error
    );

    bool success = false;
    if (!dbus_error_is_set(&error) && reply) {
        log_handler_->info("Advertisement registered successfully");
        success = true;
        advertisement_registered_ = true;
    } else if (dbus_error_is_set(&error)) {
        std::string error_msg = error.message;
        std::string error_name = error.name ? error.name : "Unknown";

        // 如果错误是"Already Exists"，说明广告已经注册，视为成功
        if (dbus_error_has_name(&error, "org.bluez.Error.AlreadyExists") ||
            error_msg.find("Already Exists") != std::string::npos) {
            log_handler_->info("Advertisement already registered - treating as success");
            success = true;
            advertisement_registered_ = true;
        } else {
            log_handler_->error("Advertisement registration failed: {} ({})", error_msg, error_name);
        }
        dbus_error_free(&error);
    }

    dbus_message_unref(message);
    if (reply) {
        dbus_message_unref(reply);
    }

    return success;
}

bool BluetoothManager::unregister_advertisement() {
    if (!advertisement_registered_) {
        return true;
    }

    log_handler_->info("Unregistering BLE advertisement");

    if (!dbus_connection_) {
        return true;
    }

    std::lock_guard<std::mutex> lock(dbus_mutex_);

    // 创建UnregisterAdvertisement方法调用
    DBusMessage* message = dbus_message_new_method_call(
        BLUEZ_SERVICE,
        config_.adapter_path.c_str(),
        BLUEZ_ADVERTISING_MANAGER_INTERFACE,
        "UnregisterAdvertisement"
    );

    if (!message) {
        log_handler_->warn("Failed to create UnregisterAdvertisement message");
        return false;
    }

    // 设置参数
    DBusMessageIter iter;
    dbus_message_iter_init_append(message, &iter);

    // 参数：广告对象路径
    const char* ad_path = ADVERTISEMENT_PATH;
    dbus_message_iter_append_basic(&iter, DBUS_TYPE_OBJECT_PATH, &ad_path);

    // 发送消息
    DBusError error;
    dbus_error_init(&error);
    DBusMessage* reply = dbus_connection_send_with_reply_and_block(
        dbus_connection_, message, 5000, &error
    );

    if (dbus_error_is_set(&error)) {
        log_handler_->warn("Advertisement unregistration failed: {}", error.message);
        dbus_error_free(&error);
    } else {
        log_handler_->info("Advertisement unregistered successfully");
    }

    dbus_message_unref(message);
    if (reply) {
        dbus_message_unref(reply);
    }

    advertisement_registered_ = false;
    return true;
}

} // namespace aby_box